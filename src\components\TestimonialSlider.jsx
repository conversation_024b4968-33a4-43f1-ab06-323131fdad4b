'use client';

import React from 'react';

const TestimonialSlider = ({ testimonials }) => {
  // Show only the first testimonial or display multiple side by side
  const displayTestimonials = testimonials.slice(0, 3);

  return (
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
        {displayTestimonials.map((testimonial, index) => (
          <div key={index} className="text-center">
            <p className="text-gray-700 text-lg leading-relaxed mb-6 font-light">
              "{testimonial.quote}"
            </p>

            <div className="space-y-1">
              <h4 className="font-medium text-gray-800">
                {testimonial.author}
              </h4>
              <p className="text-gray-500 text-sm font-light">
                {testimonial.location}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TestimonialSlider;