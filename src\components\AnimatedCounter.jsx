'use client';

import { useEffect, useState, useRef } from 'react';

const AnimatedCounter = ({ 
  end, 
  duration = 2000, 
  suffix = '', 
  prefix = '',
  className = '',
  startOnView = true 
}) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(!startOnView);
  const elementRef = useRef(null);

  useEffect(() => {
    if (!startOnView) {
      setHasStarted(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasStarted) {
          setHasStarted(true);
        }
      },
      { threshold: 0.5 }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [startOnView, hasStarted]);

  useEffect(() => {
    if (!hasStarted) return;

    let startTime = null;
    const startValue = 0;
    const endValue = end;

    const animate = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentCount = Math.floor(easeOutQuart * (endValue - startValue) + startValue);
      
      setCount(currentCount);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [hasStarted, end, duration]);

  return (
    <span 
      ref={elementRef}
      className={`stat-number animate-count-up ${className}`}
    >
      {prefix}{count}{suffix}
    </span>
  );
};

// Komponent dla sekcji statystyk
export const StatsSection = ({ stats = [] }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
      {stats.map((stat, index) => (
        <div key={index} className="space-y-2">
          <div className="text-3xl lg:text-4xl font-serif font-light">
            <AnimatedCounter
              end={stat.value}
              suffix={stat.suffix || ''}
              prefix={stat.prefix || ''}
              duration={2000 + index * 200} // Stagger animations
            />
          </div>
          <p className="text-sm uppercase tracking-wider font-light opacity-70">
            {stat.label}
          </p>
        </div>
      ))}
    </div>
  );
};

export default AnimatedCounter;
