'use client';

import React, { useState } from 'react';

const EnhancedFAQ = () => {
  const [openItems, setOpenItems] = useState(new Set([0])); // Pierwszy element otwarty domyślnie

  const toggleItem = (index) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  const faqItems = [
    {
      question: "<PERSON><PERSON> muszę znać angielski na Bali?",
      answer: "Nie! Julia prowadzi wszystkie zajęcia w języku polskim. W hotelach i restauracjach podstawowy angielski wystarczy, a my jesteśmy zawsze blisko, żeby pomóc. Większość miejsc na Bali jest przystosowana do turystów."
    },
    {
      question: "C<PERSON> jest bezpiecznie dla kobiet podróżujących samotnie?",
      answer: "Absolutnie tak! Bali to jedna z najbezpieczniejszych destynacji w Azji. Podróżujemy w grupie, mieszkamy w sprawdzonych miejscach, a Julia ma 5+ lat doświadczenia w organizacji wyjazdów. 95% naszych uczestniczek to kobiety."
    },
    {
      question: "Co jest wliczone w cenę retreatu?",
      answer: "W cenę wliczone jest: zakwaterowanie (pokoje 2-osobowe), wszystkie posiłki (śniadania, obiady, kolacje), codzienne zajęcia jogi i medytacji, warsztaty tematyczne, wycieczki zgodnie z programem, transport lokalny na Bali oraz opieka instruktorki przez cały pobyt."
    },
    {
      question: "Jaki poziom jogi jest wymagany?",
      answer: "Retreat jest dostosowany do wszystkich poziomów! Julia prowadzi zajęcia z modyfikacjami dla początkujących i wyzwaniami dla zaawansowanych. Najważniejsza jest otwartość na nowe doświadczenia. 40% uczestników to osoby początkujące."
    },
    {
      question: "Ile kosztuje wyjazd i co nie jest wliczone?",
      answer: "Cena retreatu to 2900-4500 PLN (zależnie od terminu i typu pokoju). Dodatkowo płacisz: lot do Bali (ok. 2500-3500 PLN), wizę (35 USD), ubezpieczenie podróżne oraz wydatki osobiste. Pomagamy w organizacji lotów."
    },
    {
      question: "Jakie dokumenty potrzebuję do wyjazdu?",
      answer: "Potrzebujesz: paszport ważny min. 6 miesięcy od daty wyjazdu, wizę turystyczną (można kupić online za 35 USD lub na lotnisku), ubezpieczenie podróżne oraz zaświadczenie o szczepieniach (jeśli wymagane). Pomagamy w przygotowaniu dokumentów."
    },
    {
      question: "Czy mogę przyjechać z partnerem/przyjaciółką?",
      answer: "Oczywiście! Chętnie przyjmujemy pary i przyjaciółki. Możemy zorganizować wspólne pokoje. Atmosfera w grupie jest bardzo otwarta i integrująca - szybko znajdziesz nowych przyjaciół."
    },
    {
      question: "Co jeśli mam ograniczenia żywieniowe?",
      answer: "Bez problemu! Informuj nas o swoich potrzebach żywieniowych przy zapisach. Bali oferuje świetne opcje wegetariańskie, wegańskie i bezglutenowe. Wszystkie nasze posiłki są świeże, lokalne i dostosowane do różnych diet."
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-b from-shell/20 to-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-serif text-temple mb-4">
            Często Zadawane Pytania
          </h2>
          <p className="text-wood-light/80 text-lg mb-8">
            Znajdź odpowiedzi na najczęściej zadawane pytania o nasze retreaty
          </p>
          <div className="w-24 h-px bg-temple/20 mx-auto"></div>
        </div>

        <div className="space-y-4">
          {faqItems.map((item, index) => (
            <div 
              key={index}
              className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 overflow-hidden transition-all duration-300 hover:border-temple/20"
            >
              <button
                onClick={() => toggleItem(index)}
                className="w-full px-6 py-5 text-left flex justify-between items-center hover:bg-shell/30 transition-colors duration-200"
                aria-expanded={openItems.has(index)}
              >
                <h3 className="text-lg font-medium text-temple pr-4">
                  {item.question}
                </h3>
                <div className={`flex-shrink-0 w-6 h-6 transition-transform duration-300 ${openItems.has(index) ? 'rotate-180' : ''}`}>
                  <svg className="w-full h-full text-temple" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
              
              <div className={`transition-all duration-300 ease-in-out ${openItems.has(index) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
                <div className="px-6 pb-5">
                  <p className="text-wood-light/80 leading-relaxed">
                    {item.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-wood-light/70 mb-4">
            Nie znalazłeś odpowiedzi na swoje pytanie?
          </p>
          <a 
            href="https://wa.me/48606101523?text=Cześć! Mam pytanie dotyczące retreatu jogi na Bali."
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full transition-all duration-300 hover:scale-105"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
            </svg>
            Napisz na WhatsApp
          </a>
        </div>
      </div>
    </section>
  );
};

export default EnhancedFAQ;
