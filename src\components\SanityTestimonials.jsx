import React from 'react';
import Image from 'next/image';
import { getFeaturedTestimonials, urlFor } from '../../lib/sanity';

// Fallback testimonials jeśli Sanity nie jest jeszcze skonfigurowane
const fallbackTestimonials = [
  {
    _id: 'fallback-1',
    name: '<PERSON>',
    content: 'Retreat z Julią to naj<PERSON><PERSON><PERSON>, co mogłam dla siebie zrobić. Połączenie jogi, medytacji i eksploracji Bali było idealnie wyważone. Wróciłam odmieniona!',
    rating: 5,
    location: 'Warszawa',
    retreatDate: 'Maj 2024'
  },
  {
    _id: 'fallback-2',
    name: '<PERSON><PERSON>',
    content: 'Jako pocz<PERSON>tkuj<PERSON>cy w jodze obawiałem się, czy dam radę. Julia stworzyła przestrzeń, w której każdy mógł praktykować na swoim poziomie. Bali zachwyciło mnie!',
    rating: 5,
    location: 'Kraków',
    retreatDate: 'Sierpie<PERSON> 2024'
  },
  {
    _id: 'fallback-3',
    name: '<PERSON><PERSON><PERSON>',
    content: 'Trzeci raz uczestniczę w retreatach Julii i za każdym razem odkrywam coś nowego - zarówno w praktyce jogi, jak i w sobie. Polecam z całego serca!',
    rating: 5,
    location: 'Wrocław',
    retreatDate: 'Październik 2024'
  }
];

const StarRating = ({ rating }) => {
  return (
    <div className="flex items-center gap-1 mb-4">
      {[...Array(5)].map((_, i) => (
        <span
          key={i}
          className={`text-lg ${
            i < rating ? 'text-yellow-400' : 'text-gray-300'
          }`}
        >
          ⭐
        </span>
      ))}
    </div>
  );
};

const TestimonialCard = ({ testimonial }) => {
  const initials = testimonial.name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-temple/10 p-6 hover:border-temple/20 transition-all duration-300 hover:scale-105">
      <StarRating rating={testimonial.rating} />
      
      <p className="text-wood-light/80 leading-relaxed mb-6 italic">
        "{testimonial.content}"
      </p>
      
      <div className="flex items-center">
        <div className="w-12 h-12 rounded-full overflow-hidden bg-temple/10 flex items-center justify-center mr-4">
          {testimonial.photo && urlFor(testimonial.photo) ? (
            <Image
              src={urlFor(testimonial.photo).width(48).height(48).url()}
              alt={testimonial.photo.alt || testimonial.name}
              width={48}
              height={48}
              className="object-cover rounded-full"
            />
          ) : (
            <span className="text-temple font-serif text-lg">
              {initials}
            </span>
          )}
        </div>
        
        <div>
          <p className="font-medium text-temple">{testimonial.name}</p>
          <p className="text-sm text-wood-light/60">
            {testimonial.location}
            {testimonial.retreatDate && `, ${testimonial.retreatDate}`}
          </p>
        </div>
      </div>
    </div>
  );
};

const SanityTestimonials = async () => {
  let testimonials = [];
  
  try {
    // Spróbuj pobrać testimoniale z Sanity
    testimonials = await getFeaturedTestimonials();
    
    // Jeśli nie ma testimoniali w Sanity, użyj fallback
    if (!testimonials || testimonials.length === 0) {
      testimonials = fallbackTestimonials;
    }
  } catch (error) {
    console.log('Sanity not configured yet, using fallback testimonials');
    testimonials = fallbackTestimonials;
  }

  return (
    <section className="py-16 bg-gradient-to-b from-shell/20 to-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="subtitle mb-4">Opinie</div>
          <h2 className="text-3xl md:text-4xl font-serif text-temple mb-4">
            Co mówią uczestnicy
          </h2>
          <p className="text-wood-light/80 text-lg mb-8">
            Poznaj opinie osób, które doświadczyły naszych retreatów jogowych na Bali
          </p>
          <div className="w-24 h-px bg-temple/20 mx-auto"></div>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.slice(0, 6).map((testimonial, index) => (
            <div
              key={testimonial._id}
              className="animate-fade-in"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <TestimonialCard testimonial={testimonial} />
            </div>
          ))}
        </div>

        {/* Statystyki zaufania */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div className="p-4">
            <div className="text-3xl font-light text-temple mb-2 font-serif">47+</div>
            <div className="text-sm text-wood-light/60">Zadowolonych uczestników</div>
          </div>
          <div className="p-4">
            <div className="text-3xl font-light text-temple mb-2 font-serif">4.9★</div>
            <div className="text-sm text-wood-light/60">Średnia ocen</div>
          </div>
          <div className="p-4">
            <div className="text-3xl font-light text-temple mb-2 font-serif">100%</div>
            <div className="text-sm text-wood-light/60">Bezpieczne wyjazdy</div>
          </div>
          <div className="p-4">
            <div className="text-3xl font-light text-temple mb-2 font-serif">5+</div>
            <div className="text-sm text-wood-light/60">Lat doświadczenia</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SanityTestimonials;
