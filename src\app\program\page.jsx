import React from 'react';
import Image from 'next/image';
import { getProgramByDestination, getAllDestinations, destinations } from '../../data/programData';
import { generateMetadata as generateSEOMetadata } from '../metadata';

export const metadata = generateSEOMetadata({
  title: 'Retreaty Jogi Bali - Duchowe Serce Azji | BAKASANA',
  description: 'Transformacyjne retreaty jogi na Bali z Julią Jakubowicz. Ubud, Gili Air, Canggu - odkryj duchowe serce Azji. Codzienne praktyki jogi, świątynie hinduistyczne, tarasy ryżowe. Małe grupy, autentyczne doświadczenia.',
  keywords: [
    'retreat jogi bali',
    'ubud joga',
    'gili air retreat',
    'canggu joga klify',
    'świątynie hinduistyczne bali',
    'tarasy ryżowe joga',
    'julia jak<PERSON> bali',
    'duchowe podróże bali',
    'transformacyjne retreaty',
    'kelingking nusa penida'
  ],
});

export default async function ProgramPage({ searchParams }) {
  // Get destination from URL params, default to 'bali'
  const params = await searchParams;
  const selectedDestination = params?.destination || 'bali';
  const currentDestination = destinations[selectedDestination] || destinations.bali;
  const programDays = getProgramByDestination(selectedDestination);
  const allDestinations = getAllDestinations();

  const includedServices = [
    'Zakwaterowanie w komfortowych hotelach',
    'Codzienne praktyki jogi (poranne i wieczorne)',
    'Śniadania w hotelach',
    'Transfery między lokalizacjami',
    'Opieka polskojęzycznego przewodnika',
    'Ubezpieczenie grupowe',
    'Wsparcie 24/7 podczas całego pobytu'
  ];

  const additionalCosts = [
    'Bilety lotnicze (ok. 2500-3500 zł)',
    'Obiady i kolacje (ok. 15-30 zł za posiłek)',
    'Wiza do Indonezji (35 USD)',
    'Wycieczki fakultatywne',
    'Huśtawka nad tarasami ryżowymi (ok. 15 USD)',
    'Wschód słońca na wulkanie (ok. 50 USD)'
  ];

  return (
    <main className="relative min-h-screen bg-gradient-to-b from-rice/90 via-mist/50 to-ocean-light/10">
      {/* Hero Section */}
      <section className="section section-padding">
        <div className="max-width-content container-padding">
          <div className="section-title">
            <h1 className="text-4xl md:text-6xl font-serif text-temple tracking-tight mb-6 leading-tight font-light">
              Program <span className="text-golden">Retreatu</span>
            </h1>
            <p className="text-lg text-wood-light leading-relaxed font-light">
              Szczegółowy harmonogram {currentDestination.duration} retreatu jogowego - {currentDestination.name}
            </p>
            <div className="decorative-line" />
          </div>

          {/* Destination Selector */}
          <div className="flex flex-wrap gap-4 justify-center mb-12">
            {allDestinations.map((dest) => (
              <a
                key={dest.id}
                href={`/program?destination=${dest.id}`}
                className={`px-6 py-3 rounded-lg border transition-all duration-300 ${
                  selectedDestination === dest.id
                    ? 'bg-temple text-white border-temple'
                    : 'bg-white/50 text-temple border-temple/20 hover:border-temple/40'
                }`}
              >
                <div className="text-center">
                  <div className="font-serif text-lg">{dest.name}</div>
                  <div className="text-sm opacity-70">{dest.duration}</div>
                </div>
              </a>
            ))}
          </div>

          {/* Current Destination Info */}
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h2 className="text-2xl font-serif text-temple mb-4">{currentDestination.name}</h2>
                <p className="text-wood-light mb-4">{currentDestination.description}</p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {currentDestination.highlights.map((highlight) => (
                    <span key={highlight} className="px-3 py-1 bg-temple/10 text-temple text-sm rounded-full">
                      {highlight}
                    </span>
                  ))}
                </div>
                <div className="text-golden font-medium">{currentDestination.priceRange}</div>
              </div>
              <div className="relative h-64 rounded-lg overflow-hidden">
                <Image
                  src={currentDestination.image}
                  alt={`Retreat jogowy ${currentDestination.name}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Daily Program */}
      <section className="section section-padding">
        <div className="max-width-content container-padding">
          <div className="section-title">
            <h2 className="text-3xl font-serif text-temple mb-4 font-light">
              Program Dzień po Dniu
            </h2>
            <p className="text-wood-light font-light">Szczegółowy harmonogram {currentDestination.duration} retreatu - {currentDestination.name}</p>
            <div className="decorative-line" />
          </div>

          <div className="space-y-12">
            {programDays.map((day, index) => (
              <div key={day.day} className="card p-8">
                <div className={`md:flex items-start gap-12 ${index % 2 === 1 ? 'md:flex-row-reverse' : ''}`}>
                  {/* Image Section */}
                  <div className="md:w-2/5 relative h-64 md:h-80 mb-6 md:mb-0 rounded-lg overflow-hidden">
                    {day.image ? (
                      <Image
                        src={day.image}
                        alt={`Dzień ${day.day} - ${day.title}`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, 40vw"
                      />
                    ) : (
                      <div className="w-full h-full bg-temple/5 flex items-center justify-center">
                        <span className="text-temple/60 text-xl font-serif font-light">Dzień {day.day}</span>
                      </div>
                    )}

                    <div className="absolute inset-0 bg-temple/5"></div>
                  </div>

                  {/* Content Section */}
                  <div className="md:w-3/5">
                    <div className="mb-5">
                      <h3 className="text-2xl font-serif text-temple leading-tight mb-3 font-light">
                        {day.title}
                      </h3>
                    </div>

                    <ul className="space-y-3">
                      {day.activities.map((activity, actIndex) => (
                        <li key={actIndex} className="flex items-start gap-3">
                          <div className="w-1 h-1 bg-temple/40 mt-2 flex-shrink-0" />
                          <span className="text-wood-light leading-relaxed font-light">
                            {activity}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* What's Included */}
      <section className="section section-padding">
        <div className="max-width-content container-padding">
          <div className="section-title">
            <h2 className="text-3xl font-serif text-temple mb-6 font-light">
              Szczegóły Pakietu
            </h2>
            <p className="text-wood-light mb-8 font-light">Co zawiera cena i jakie są dodatkowe koszty</p>
            <div className="decorative-line" />
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Included */}
            <div className="card p-8">
              <h3 className="text-lg font-serif text-temple mb-4 font-light">
                W cenie zawarte
              </h3>

              <div className="space-y-3">
                {includedServices.map((service, index) => (
                  <div key={index} className="flex items-start gap-3 py-2 border-b border-temple/10">
                    <div className="w-1 h-1 bg-sage/60 mt-2 flex-shrink-0" />
                    <span className="text-wood-light leading-relaxed font-light">
                      {service}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional Costs */}
            <div className="card p-8">
              <h3 className="text-lg font-serif text-temple mb-4 font-light">
                Koszty dodatkowe
              </h3>

              <div className="space-y-3">
                {additionalCosts.map((cost, index) => (
                  <div key={index} className="flex items-start gap-3 py-2 border-b border-temple/10">
                    <div className="w-1 h-1 bg-golden/60 mt-2 flex-shrink-0" />
                    <span className="text-wood-light leading-relaxed font-light">
                      {cost}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Important Info */}
      <section className="section section-padding">
        <div className="max-width-content container-padding">
          <div className="section-title">
            <h3 className="text-3xl font-serif text-temple mb-6 font-light">
              Ważne Informacje
            </h3>
            <p className="text-wood-light/85 mb-8 font-light">Wszystko co musisz wiedzieć przed wyjazdem</p>
            <div className="decorative-line" />
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="card p-6 space-y-6">
              <div>
                <h4 className="text-lg font-serif text-temple/85 mb-3 font-light">Poziom trudności</h4>
                <p className="text-wood-light/80 leading-relaxed font-light">Program dostosowany do wszystkich poziomów zaawansowania. Każdy znajdzie coś dla siebie - od początkujących po zaawansowanych joginów.</p>
              </div>

              <div>
                <h4 className="text-lg font-serif text-temple/85 mb-3 font-light">Grupa</h4>
                <p className="text-wood-light/80 leading-relaxed font-light">Maksymalnie 12 osób, co gwarantuje indywidualne podejście i kameralną atmosferę.</p>
              </div>
            </div>

            <div className="card p-6 space-y-6">
              <div>
                <h4 className="text-lg font-serif text-temple/85 mb-3 font-light">Wyposażenie</h4>
                <p className="text-wood-light/80 leading-relaxed font-light">Maty do jogi zapewnione. Zalecamy zabranie własnej odzieży sportowej i wygodnych butów.</p>
              </div>

              <div>
                <h4 className="text-lg font-serif text-temple/85 mb-3 font-light">Pogoda</h4>
                <p className="text-wood-light/80 leading-relaxed font-light">Bali ma klimat tropikalny - ciepło przez cały rok (26-30°C). Możliwe krótkie opady deszczu.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section section-padding">
        <div className="max-width-content container-padding">
          <div className="glass-effect bg-gradient-to-r from-bamboo/10 to-lotus/20 rounded-3xl p-12 text-center">
            <h3 className="text-2xl font-serif text-temple mb-6 font-light">
              Gotowa na przygodę życia?
            </h3>

            <p className="text-wood-light/85 leading-relaxed mb-8 font-light">
              Dołącz do naszego wyjątkowego retreatu na Bali. Odkryj magię jogi w tropikalnym raju
              i wróć do domu z nową energią i perspektywą.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="/kontakt"
                className="btn-soft-golden"
                aria-label="Zapytaj o dostępność retreatu jogowego na Bali"
              >
                Zapytaj o Dostępność
              </a>

              <span className="text-temple/50 text-sm">lub</span>

              <a
                href="/o-mnie"
                className="btn-soft"
                aria-label="Poznaj instruktorkę Julię Jakubowicz"
              >
                Poznaj Instruktorkę
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* WhatsApp Button - floating */}
      <a
        href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:scale-110 transition-all duration-300 z-50 group"
        aria-label="Skontaktuj się przez WhatsApp"
      >
        <svg
          className="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>

        {/* Tooltip */}
        <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
          Napisz na WhatsApp
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      </a>
    </main>
  );
}