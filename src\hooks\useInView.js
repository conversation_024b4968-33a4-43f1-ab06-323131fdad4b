// hooks/useInView.js - POPRAWIONA WERSJA
import { useState, useEffect, useRef, useCallback } from 'react';

export function useInView(options = {}) {
  const { threshold = 0.1, triggerOnce = false, rootMargin = '0px' } = options;
  const [inView, setInView] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const ref = useRef(null);
  const observerRef = useRef(null);

  const handleIntersect = useCallback((entries) => {
    const [entry] = entries;
    const isIntersecting = entry.isIntersecting;
    
    if (isIntersecting && triggerOnce && !hasTriggered) {
      setInView(true);
      setHasTriggered(true);
      // Odłącz observer po pierwszym uruchomieniu
      if (observerRef.current && ref.current) {
        observerRef.current.unobserve(ref.current);
      }
    } else if (!triggerOnce) {
      setInView(isIntersecting);
    }
  }, [triggerOnce, hasTriggered]);

  useEffect(() => {
    const element = ref.current;
    
    // Sprawdź czy IntersectionObserver jest dostępny
    if (!element || typeof IntersectionObserver === 'undefined') {
      // Fallback - pokaż element od razu
      setInView(true);
      return;
    }

    // Jeśli już zostało uruchomione i jest triggerOnce, nie twórz nowego observera
    if (triggerOnce && hasTriggered) {
      return;
    }

    const observer = new IntersectionObserver(handleIntersect, {
      threshold,
      rootMargin,
    });

    observerRef.current = observer;
    observer.observe(element);

    return () => {
      if (observer && element) {
        observer.unobserve(element);
      }
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [handleIntersect, threshold, rootMargin, triggerOnce, hasTriggered]);

  // Cleanup przy unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return { ref, inView };
}