'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, Phone, Mail, Calendar, Plus, X } from 'lucide-react';

const FloatingActionButton = () => {
  const [isOpen, setIsOpen] = useState(false);

  const actions = [
    {
      icon: MessageCircle,
      label: 'WhatsApp',
      href: 'https://wa.me/48123456789',
      color: 'bg-green-500',
      delay: 0.1
    },
    {
      icon: Phone,
      label: 'Zadzwo<PERSON>',
      href: 'tel:+48123456789',
      color: 'bg-blue-500',
      delay: 0.2
    },
    {
      icon: Mail,
      label: 'Email',
      href: 'mailto:<EMAIL>',
      color: 'bg-purple-500',
      delay: 0.3
    },
    {
      icon: Calendar,
      label: 'Rezerwuj',
      href: '/kontakt',
      color: 'bg-accent',
      delay: 0.4
    }
  ];

  const containerVariants = {
    open: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    },
    closed: {
      transition: {
        staggerChildren: 0.05,
        staggerDirection: -1
      }
    }
  };

  const itemVariants = {
    open: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24
      }
    },
    closed: {
      y: 20,
      opacity: 0,
      scale: 0.3,
      transition: {
        duration: 0.2
      }
    }
  };

  const mainButtonVariants = {
    open: { rotate: 45 },
    closed: { rotate: 0 }
  };

  return (
    <div className="fixed bottom-6 right-6 z-40">
      {/* Action Items */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="flex flex-col items-end space-y-3 mb-4"
            variants={containerVariants}
            initial="closed"
            animate="open"
            exit="closed"
          >
            {actions.map((action, index) => (
              <motion.a
                key={action.label}
                href={action.href}
                className={`
                  group flex items-center space-x-3 ${action.color} text-white 
                  px-4 py-3 rounded-full shadow-lg hover:shadow-xl
                  transition-all duration-300 backdrop-blur-sm
                  border border-white/20
                `}
                variants={itemVariants}
                whileHover={{ 
                  scale: 1.05,
                  x: -5
                }}
                whileTap={{ scale: 0.95 }}
                target={action.href.startsWith('http') ? '_blank' : '_self'}
                rel={action.href.startsWith('http') ? 'noopener noreferrer' : undefined}
              >
                <action.icon className="w-5 h-5" />
                <span className="font-medium text-sm whitespace-nowrap pr-2">
                  {action.label}
                </span>
              </motion.a>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB Button */}
      <motion.button
        className="
          w-14 h-14 bg-accent hover:bg-accent/90 text-white 
          rounded-full shadow-lg hover:shadow-xl
          flex items-center justify-center
          transition-all duration-300
          border-2 border-white/20
          backdrop-blur-sm
        "
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        variants={mainButtonVariants}
        animate={isOpen ? "open" : "closed"}
        aria-label={isOpen ? 'Zamknij menu kontakt' : 'Otwórz menu kontakt'}
      >
        <AnimatePresence mode="wait">
          {isOpen ? (
            <motion.div
              key="close"
              initial={{ rotate: -90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: 90, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <X className="w-6 h-6" />
            </motion.div>
          ) : (
            <motion.div
              key="open"
              initial={{ rotate: 90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: -90, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <MessageCircle className="w-6 h-6" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/20 backdrop-blur-sm -z-10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default FloatingActionButton;