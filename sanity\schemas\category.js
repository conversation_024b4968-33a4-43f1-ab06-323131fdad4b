// sanity/schemas/category.js
export default {
  name: 'category',
  title: 'Kategorie bloga',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Nazwa kategorii',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'slug',
      title: 'URL (slug)',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    },
    {
      name: 'description',
      title: 'Opis kategorii',
      type: 'text'
    },
    {
      name: 'color',
      title: 'Kolor kategorii',
      type: 'string',
      options: {
        list: [
          {title: '<PERSON><PERSON><PERSON><PERSON>', value: 'blue'},
          {title: '<PERSON>iel<PERSON>', value: 'green'},
          {title: 'Pomarańczowy', value: 'orange'},
          {title: 'Fioletowy', value: 'purple'},
          {title: 'Różowy', value: 'pink'},
          {title: '<PERSON><PERSON><PERSON><PERSON>', value: 'yellow'}
        ]
      }
    }
  ],
  preview: {
    select: {
      title: 'title',
      description: 'description'
    }
  }
}
