'use client';
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-light transition-all duration-300 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 rectangular elegant-border",
  {
    variants: {
      variant: {
        primary: "bg-accent/90 text-secondary hover:bg-accent transition-colors focus:ring-accent/30",
        secondary: "bg-secondary text-accent border border-accent/10 hover:bg-secondary/80 transition-colors focus:ring-accent/20",
        outline: "bg-transparent border border-accent/20 text-accent hover:bg-accent/5 transition-colors focus:ring-accent/20",
        ghost: "bg-transparent text-accent hover:bg-accent/5 transition-colors focus:ring-accent/20",
        hero: "bg-white/10 backdrop-blur-sm text-accent/85 border border-white/10 hover:bg-white/20 transition-all focus:ring-white/20",
        accent: "bg-accent/90 text-secondary hover:bg-accent transition-colors focus:ring-accent/30",
      },
      size: {
        sm: "px-3 py-1.5 text-xs",
        default: "px-6 py-2 text-sm",
        lg: "px-8 py-3 text-base",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
)

const Button = React.forwardRef(({ 
  className, 
  variant, 
  size, 
  asChild = false, 
  ...props 
}, ref) => {
  const Comp = asChild ? Slot : "button"
  return (
    <Comp
      className={cn(buttonVariants({ variant, size, className }))}
      ref={ref}
      {...props}
    />
  )
})
Button.displayName = "Button"

export { Button, buttonVariants }
