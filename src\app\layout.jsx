import './globals.css'
import { Playfair_Display } from 'next/font/google'
import ClientNavbar from '@/components/Navbar/ClientNavbar'
import Footer from '@/components/Footer'
import QuickCTA from '@/components/QuickCTA'

const playfair = Playfair_Display({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-playfair'
})

export const metadata = {
  title: 'BAKASANA - Retreaty Jogi Bali & Sri Lanka | Transformacyjne Podróże z Julią Jakubowicz',
  description: 'Odkryj duchowe serce Azji na retreatach jogi z Julią Jakubowicz. Bali, Sri Lanka, Ubud, Gili Air - transformacyjne doświadczenia w najpiękniejszych miejscach świata. Joga, medytacja, ayurveda i wewnętrzna przemiana.',
  keywords: 'retreaty jogi bali, joga sri lanka, julia jak<PERSON>, ubud joga, gili air retreat, transformacyjne podróże, medyta<PERSON><PERSON> bali, ayurveda sri lanka, retreat jogi a<PERSON>ja, duchowa podróż, joga wakac<PERSON>',
  openGraph: {
    title: 'BAKASANA - Retreaty Jogi Bali & Sri Lanka',
    description: 'Transformacyjne retreaty jogi w duchowym sercu Azji. Odkryj Bali i Sri Lankę z doświadczoną instruktorką Julią Jakubowicz.',
    images: ['/images/background/bali-hero.webp'],
    type: 'website',
    locale: 'pl_PL',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BAKASANA - Retreaty Jogi Bali & Sri Lanka',
    description: 'Transformacyjne doświadczenia jogi w najpiękniejszych miejscach Azji',
    images: ['/images/background/bali-hero.webp'],
  }
}

export default function RootLayout({ children }) {
  return (
    <html lang="pl" className={playfair.variable}>
      <body>
        <ClientNavbar />
        <main>{children}</main>
        <Footer />
        <QuickCTA />
      </body>
    </html>
  )
}