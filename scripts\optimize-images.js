const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const glob = require('glob');

// Konfiguracja
const config = {
  inputDir: path.join(__dirname, '../public/images'),
  outputDir: path.join(__dirname, '../public/images/optimized'),
  formats: ['webp', 'avif'],
  sizes: [640, 750, 828, 1080, 1200, 1920],
  quality: 80
};

// Utwórz katalog wyjściowy, jeśli nie istnieje
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Znajdź wszystkie obrazy
const imageFiles = glob.sync(`${config.inputDir}/**/*.{jpg,jpeg,png}`);

// Funkcja do optymalizacji obrazu
async function optimizeImage(file) {
  const filename = path.basename(file, path.extname(file));
  const image = sharp(file);
  const metadata = await image.metadata();

  // Dla każdego rozmiaru
  for (const size of config.sizes) {
    // Przeskaluj tylko jeśli obraz jest większy niż docelowy rozmiar
    if (metadata.width > size) {
      // Dla każdego formatu
      for (const format of config.formats) {
        const outputPath = path.join(
          config.outputDir,
          `${filename}-${size}.${format}`
        );

        // Optymalizuj i zapisz
        await image
          .resize(size)
          [format]({ quality: config.quality })
          .toFile(outputPath);

        console.log(`Zoptymalizowano: ${outputPath}`);
      }
    }
  }

  // Zawsze generuj wersję oryginalnego rozmiaru
  for (const format of config.formats) {
    const outputPath = path.join(
      config.outputDir,
      `${filename}.${format}`
    );

    await image
      [format]({ quality: config.quality })
      .toFile(outputPath);

    console.log(`Zoptymalizowano: ${outputPath}`);
  }
}

// Przetwórz wszystkie obrazy
async function processAllImages() {
  console.log(`Znaleziono ${imageFiles.length} obrazów do optymalizacji...`);
  
  for (const file of imageFiles) {
    try {
      await optimizeImage(file);
    } catch (error) {
      console.error(`Błąd podczas optymalizacji ${file}:`, error);
    }
  }
  
  console.log('Optymalizacja zakończona!');
}

processAllImages();