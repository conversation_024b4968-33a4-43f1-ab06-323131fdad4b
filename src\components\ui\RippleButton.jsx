'use client';

import React, { useState, useRef } from 'react';
import { cn } from '@/lib/utils';

const RippleButton = React.forwardRef(({ 
  className, 
  children, 
  variant = 'primary',
  size = 'default',
  disabled = false,
  onClick,
  ...props 
}, ref) => {
  const [ripples, setRipples] = useState([]);
  const buttonRef = useRef(null);

  const createRipple = (event) => {
    const button = buttonRef.current;
    if (!button) return;

    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const newRipple = {
      x,
      y,
      size,
      id: Date.now(),
    };

    setRipples(prev => [...prev, newRipple]);

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, 600);
  };

  const handleClick = (event) => {
    if (!disabled) {
      createRipple(event);
      onClick?.(event);
    }
  };

  const baseClasses = "relative overflow-hidden transition-all duration-300 font-light tracking-wide";
  
  const variants = {
    primary: "bg-accent/90 text-secondary hover:bg-accent focus:ring-accent/30 border border-accent/20",
    secondary: "bg-secondary text-accent border border-accent/20 hover:bg-accent/5 focus:ring-accent/20",
    outline: "bg-transparent border border-accent/30 text-accent hover:bg-accent/10 focus:ring-accent/20",
    ghost: "bg-transparent text-accent hover:bg-accent/5 focus:ring-accent/20",
    elegant: "bg-gradient-elegant text-accent border border-accent/10 hover:bg-accent/5 focus:ring-accent/20 backdrop-blur-sm",
  };

  const sizes = {
    sm: "px-4 py-2 text-sm rounded-lg",
    default: "px-6 py-3 text-sm rounded-xl",
    lg: "px-8 py-4 text-base rounded-xl",
    xl: "px-10 py-5 text-lg rounded-2xl",
  };

  return (
    <button
      ref={buttonRef}
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleClick}
      disabled={disabled}
      {...props}
    >
      {/* Ripple effects */}
      {ripples.map((ripple) => (
        <span
          key={ripple.id}
          className="absolute bg-white/30 rounded-full animate-ping pointer-events-none"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            animationDuration: '600ms',
            animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
        />
      ))}
      
      {/* Button content */}
      <span className="relative z-10">
        {children}
      </span>
    </button>
  );
});

RippleButton.displayName = "RippleButton";

export default RippleButton;
