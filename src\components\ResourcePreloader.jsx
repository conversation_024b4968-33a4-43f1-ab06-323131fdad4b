'use client';

import { useEffect } from 'react';

export default function ResourcePreloader() {
  useEffect(() => {
    // Preload critical images with priority
    const criticalImages = [
      { src: '/images/background/bali-hero.webp', priority: 'high' },
      { src: '/images/profile/omnie-opt.webp', priority: 'high' },
      { src: '/images/gallery/ubud-rice-terraces.webp', priority: 'medium' },
      { src: '/images/gallery/uluwatu-temple.webp', priority: 'medium' }
    ];

    criticalImages.forEach(({ src, priority }) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      link.fetchPriority = priority;
      document.head.appendChild(link);
    });

    // DNS prefetch and preconnect for external domains
    const externalConnections = [
      { domain: 'https://fonts.googleapis.com', type: 'preconnect' },
      { domain: 'https://fonts.gstatic.com', type: 'preconnect', crossorigin: true },
      { domain: 'https://images.unsplash.com', type: 'dns-prefetch' },
      { domain: 'https://api.mapbox.com', type: 'dns-prefetch' },
      { domain: 'https://events.mapbox.com', type: 'dns-prefetch' },
      { domain: 'https://vitals.vercel-insights.com', type: 'dns-prefetch' },
      { domain: 'https://va.vercel-scripts.com', type: 'dns-prefetch' }
    ];

    externalConnections.forEach(({ domain, type, crossorigin }) => {
      const link = document.createElement('link');
      link.rel = type;
      link.href = domain;
      if (crossorigin) link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });

    // Prefetch next likely pages
    const prefetchPages = [
      '/blog',
      '/program',
      '/o-mnie',
      '/rezerwacja',
      '/mapa',
      '/galeria'
    ];

    // Delay prefetch to not interfere with critical loading
    setTimeout(() => {
      prefetchPages.forEach(href => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = href;
        document.head.appendChild(link);
      });
    }, 2000);

    // Preload service worker
    if ('serviceWorker' in navigator) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'script';
      link.href = '/sw.js';
      document.head.appendChild(link);
    }

    // Performance monitoring for Core Web Vitals
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            // Log performance metrics in development
            if (process.env.NODE_ENV === 'development') {
              if (entry.entryType === 'largest-contentful-paint') {
                console.log('LCP:', entry.startTime);
              }
              if (entry.entryType === 'first-input') {
                console.log('FID:', entry.processingStart - entry.startTime);
              }
              if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                console.log('CLS:', entry.value);
              }
            }
          });
        });

        observer.observe({
          entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift']
        });
      } catch (e) {
        // Fallback for older browsers
        console.log('Performance Observer not fully supported');
      }
    }

  }, []);

  return null;
}
