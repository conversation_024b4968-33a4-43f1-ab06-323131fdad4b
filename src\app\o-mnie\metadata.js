export const metadata = {
  // metadataBase zostanie odziedziczone z layout.js
  title: 'O <PERSON><PERSON> - <PERSON> - Joga i Podróże na Bali',
  description:
    'Poznaj <PERSON>, fizjoterapeutkę i instruktorkę jogi z 5-letnim doświadczeniem. Odkryj jej pasję do jogi, fizjoterapii i organizacji retreatów na Bali.',
  keywords: [
    'o mnie joga', 'instruktor jogi bali', 'julia j<PERSON><PERSON>', 'fizjoterapia joga', 'retreaty joga bali', 'slow travel bali', 'nauczyciel jogi'
  ],
  alternates: {
    canonical: '/o-mnie' // Upewnij się, że ścieżka jest poprawna
  },
   openGraph: {
    title: 'O Mnie - <PERSON> - Joga i Podróże na Bali',
    description: 'Poznaj <PERSON><PERSON><PERSON>, instruktorkę jogi i pasjonatkę podróży.',
    images: [
      {
        url: '/omnie-opt.webp', // Obrazek profilowy dla OG
        width: 500, // Do<PERSON><PERSON>j wym<PERSON>, jeśli są inne
        height: 300,
        alt: '<PERSON>akubowicz - instruktorka jogi na Bali',
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
    'max-image-preview': 'large',
    'max-video-preview': -1,
    'max-snippet': -1,
  }
};

export async function generateMetadata() {
  return metadata;
}