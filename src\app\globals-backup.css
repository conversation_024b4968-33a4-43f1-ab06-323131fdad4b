@tailwind base;
@tailwind components;
@tailwind utilities;

/* ULTRA-MINIMAL GLOBALS - ONLY ESSENTIALS */

:root {
  --warm-white: #FEFDF8;
  --charcoal: #3A3432;
  --gold: #C9A961;
  --sage: #7C9885;
  --gray: #6B6B6B;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
}

body {
  font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--warm-white);
  overflow-x: hidden;
}

/* TYPOGRAPHY */
h1, h2, h3 {
  font-family: 'Playfair Display', serif;
  font-weight: 400;
  letter-spacing: 0.02em;
  line-height: 1.2;
}

p {
  margin-bottom: 1.5rem;
}

a {
  color: inherit;
  text-decoration: none;
  transition: opacity 0.3s ease;
}

a:hover {
  opacity: 0.6;
}

img {
  max-width: 100%;
  height: auto;
}

/* LAYOUT */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 5%;
}

.section {
  padding: 80px 0;
}

/* COMPONENTS */
.btn {
  padding: 15px 40px;
  border: 1px solid currentColor;
  background: transparent;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
}

.btn:hover {
  background: currentColor;
  color: var(--warm-white);
}

/* UTILITIES */
.text-center { text-align: center; }
.mt-8 { margin-top: 2rem; }
.mb-8 { margin-bottom: 2rem; }
.fade-in { 
  animation: fadeIn 0.6s ease-out forwards; 
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* MINIMAL SCROLLBAR */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--sage);
  opacity: 0.3;
}
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: rgb(var(--color-secondary));
    /* Enhanced performance and quality */
    will-change: transform;
    backface-visibility: hidden;
  }

  .hero-content {
    position: relative;
    z-index: 10;
    text-align: center;
    max-width: 72rem;
    margin: 0 auto;
    padding: 0 2rem;
    /* Enhanced animation performance */
    will-change: transform, opacity;
  }

  .hero-title {
    font-family: 'Playfair Display', 'Didot', 'Bodoni MT', serif;
    font-size: clamp(48px, 8vw, 96px);
    font-weight: 300;
    letter-spacing: 0.15em;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: white;
    text-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.4),
      0 8px 24px rgba(0, 0, 0, 0.2),
      0 1px 2px rgba(0, 0, 0, 0.6);
    /* Premium animation performance */
    will-change: transform;
    /* Typography enhancement */
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .hero-subtitle {
    font-family: 'Source Sans Pro', 'Helvetica Neue', sans-serif;
    font-size: clamp(18px, 3vw, 28px);
    font-weight: 300;
    letter-spacing: 0.1em;
    line-height: 1.4;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* Premium button styles - GHOST BUTTONS FOR HERO */
  .hero .btn-primary {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 18px 50px;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    background: transparent;
    color: white;
    border: 2px solid white;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    overflow: hidden;
    border-radius: 0;
  }

  .hero .btn-primary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  /* Default button styles for non-hero sections */
  .btn-primary {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2.5rem;
    font-size: 0.95rem;
    font-weight: 400;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    text-decoration: none;
    overflow: hidden;
    border-radius: 0;
  }

  .btn-primary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  .btn-outline {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    font-size: 0.9rem;
    font-weight: 300;
    letter-spacing: 0.05em;
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    text-decoration: none;
    border-radius: 0;
  }

  .btn-outline:hover {
    color: white;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-1px);
  }

  /* Enhanced hero background effects */
  .hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0) 50%,
      rgba(0, 0, 0, 0.2) 100%
    );
    z-index: 5;
    pointer-events: none;
  }

  /* Scroll indicator */
  .scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
  }

  .scroll-indicator:hover {
    color: white;
    transform: translateX(-50%) translateY(-4px);
  }

  /* ==========================================================================
     7. BUTTONS - GHOST STYLE ONLY (ULTRA-MINIMALIST LUXURY)
     Exact specifications for luxury retreat website
     ========================================================================== */

  .button {
    display: inline-block;
    padding: 15px 40px;
    border: 1px solid rgb(var(--color-primary)); /* warm anthracite */
    background: transparent;
    color: rgb(var(--color-primary)); /* warm anthracite text */
    font: 12px 'Helvetica Neue', sans-serif;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border-radius: 0; /* no rounded corners */
    cursor: pointer;
    text-decoration: none;
  }

  .button:hover {
    border-color: rgb(var(--color-accent-gold)); /* gold border on hover */
    color: rgb(var(--color-accent-gold)); /* gold text on hover */
    background: transparent; /* Keep transparent - no filled background */
    /* Luxury gold accent instead of filled background */
  }

  /* Legacy button styles maintained for compatibility */
  .btn-primary {
    padding: 1rem 2rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: transparent;
    border: 1px solid rgb(var(--color-primary) / 0.3);
    color: rgb(var(--color-primary));
    transition: all 0.3s var(--ease-smooth);
    font-weight: 300;
    border-radius: 0;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-primary));
  }

  .btn-secondary {
    padding: 1rem 2rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: transparent;
    border: 1px solid rgb(var(--color-accent) / 0.3);
    color: rgb(var(--color-accent));
    transition: all 0.3s var(--ease-smooth);
    font-weight: 300;
    border-radius: 0;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: rgb(var(--color-accent));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-accent));
  }

  /* ==========================================================================
     PREMIUM NAVIGATION - LUXURY MINIMALISM
     Philosophy: Invisible when not needed, perfect when visible
     ========================================================================== */

  .navbar {
    /* Base state - completely transparent */
    backdrop-filter: none;
    background: transparent;
    border: none;
    box-shadow: none;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .navbar.scrolled {
    /* Scrolled state - glass morphism effect */
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(254, 253, 248, 0.85);
    border-bottom: 1px solid rgba(var(--color-primary), 0.08);
    box-shadow: 0 1px 24px rgba(0, 0, 0, 0.04);
  }

  .nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .nav-link {
    position: relative;
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 300;
    letter-spacing: 0.02em;
    color: rgba(var(--color-primary), 0.7);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
  }

  .nav-link:hover {
    color: rgb(var(--color-primary));
    transform: translateY(-1px);
  }

  .nav-link:active {
    transform: translateY(0);
  }

  /* Active link indicator */
  .nav-link[aria-current="page"] {
    color: rgb(var(--color-accent));
    font-weight: 400;
  }

  /* Premium hover effects */
  @media (hover: hover) {
    .nav-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(var(--color-accent), 0.05);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 4px;
      transform: scale(0.8);
    }

    .nav-link:hover::before {
      opacity: 1;
      transform: scale(1);
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 50%;
      width: 0;
      height: 2px;
      background: linear-gradient(90deg, rgb(var(--color-accent)), rgba(var(--color-accent), 0.5));
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateX(-50%);
      border-radius: 1px;
    }

    .nav-link:hover::after {
      width: 100%;
    }
  }

  /* Active state enhancements */
  .nav-link[aria-current="page"]::after {
    width: 100% !important;
    background: rgb(var(--color-accent)) !important;
  }

  .nav-link[aria-current="page"]::before {
    opacity: 1 !important;
    transform: scale(1) !important;
    background: rgba(var(--color-accent), 0.08) !important;
  }

  /* Mobile menu enhancements */
  .mobile-menu-backdrop {
    backdrop-filter: blur(10px);
    background: rgba(254, 253, 248, 0.95);
  }

  /* Logo hover effect */
  .logo-hover-effect {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .logo-hover-effect:hover {
    transform: scale(1.02);
    color: rgb(var(--color-accent));
  }

  /* Social icons */
  .social-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.6;
  }

  .social-icon:hover {
    opacity: 1;
    transform: translateY(-2px);
    color: rgb(var(--color-accent));
  }

  /* Premium navbar CTA button */
  .navbar-cta {
    position: relative;
    overflow: hidden;
    background: transparent;
    border: 1px solid rgba(var(--color-primary), 0.15);
    color: rgba(var(--color-primary), 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
  }

  .navbar-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(var(--color-accent), 0.1), transparent);
    transition: left 0.5s ease;
  }

  .navbar-cta:hover::before {
    left: 100%;
  }

  .navbar-cta:hover {
    border-color: rgba(var(--color-accent), 0.3);
    color: rgb(var(--color-accent));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--color-accent), 0.15);
  }

  /* Mobile menu CTA */
  .mobile-cta {
    background: linear-gradient(135deg, rgba(var(--color-accent), 0.1), rgba(var(--color-accent), 0.05));
    border: 1px solid rgba(var(--color-accent), 0.2);
    color: rgb(var(--color-accent));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .mobile-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgb(var(--color-accent));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
  }

  .mobile-cta:hover::before {
    transform: scaleX(1);
  }

  .mobile-cta:hover {
    color: white;
    border-color: rgb(var(--color-accent));
  }

  .mobile-cta span {
    position: relative;
    z-index: 1;
  }

  /* Premium focus states for accessibility */
  .nav-link:focus-visible,
  .navbar-cta:focus-visible,
  .mobile-cta:focus-visible {
    outline: 2px solid rgb(var(--color-accent));
    outline-offset: 2px;
    border-radius: 4px;
  }

  .btn-primary:focus-visible,
  .btn-outline:focus-visible {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .nav-link::before,
    .nav-link::after,
    .navbar-cta::before,
    .mobile-cta::before {
      transition: none;
    }
    
    .hero-title,
    .hero-subtitle {
      will-change: auto;
    }
  }

  /* Premium Typography Enhancement */
  .premium-text {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "onum" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Premium Quote Styling */
  .premium-quote {
    position: relative;
    font-style: italic;
    font-weight: 300;
    line-height: 1.6;
    padding: 2rem 0;
  }

  .premium-quote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: -30px;
    font-size: 4rem;
    color: rgba(var(--color-accent), 0.3);
    font-family: 'Playfair Display', serif;
    line-height: 1;
  }

  /* Premium Card Enhancements */
  .premium-card-enhanced {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .premium-card-enhanced:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(var(--color-accent), 0.3);
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  /* Premium Glow Effects */
  .premium-glow {
    position: relative;
  }

  .premium-glow::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    filter: blur(20px);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
  }

  .premium-glow:hover::after {
    opacity: 0.3;
  }

  /* ==========================================================================
     KARTY RETREATÓW - MINIMALIZM (RETREAT CARDS - MINIMALISM)
     Całkowicie niewidoczne kontenery z tylko treścią widoczną
     ========================================================================== */

  /* ==========================================================================
     MINIMAL CARD COMPONENTS - Clean & Simple
     ========================================================================== */

  .card {
    /* MINIMAL: No background, no borders, no shadows */
    background: transparent;
    border: none;
    border-radius: 0;
    overflow: hidden;
    transition: none;
    box-shadow: none;
  }

  .card-image {
    position: relative;
    aspect-ratio: 16/10;
    overflow: hidden;
  }

  .card-content {
    padding: 1.5rem;
  }

  /* FLOATING ELEMENTS - REMOVED FOR MINIMALISM */

  /* STATS SECTION - SIMPLIFIED */
  .stats-section {
    background: transparent;
  }

  .stat-card {
    text-align: center;
    padding: 2rem 1rem;
    transition: none;
  }

  /* NEWSLETTER SECTION - MINIMALIST */
  .newsletter-section {
    background: transparent;
    position: relative;
  }

  /* MINIMAL SECTION DIVIDERS */
  .section-divider {
    width: 60px;
    height: 1px;
    background: #E5E2DD;
    margin: 60px auto;
    border: none;
  }

  /* ==========================================================================
     SOCIAL CARDS - Contact Section
     ========================================================================== */

  .social-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(var(--color-accent), 0.1);
    border-radius: 0; /* ENTERPRISE: Prostokątne social cards */
    padding: 2rem;
    text-align: center;
    transition: all 0.3s var(--ease-smooth);
    backdrop-filter: blur(10px);
  }

  .social-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: rgba(var(--color-accent), 0.3);
  }

  .social-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    background: rgba(var(--color-accent), 0.1);
    border-radius: 0; /* ENTERPRISE: Prostokątne social icons */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s var(--ease-smooth);
  }

  .social-icon:hover {
    background: rgba(var(--color-accent), 0.2);
    transform: scale(1.1);
  }

  /* ==========================================================================
     ENHANCED JULIA QUOTE STYLING
     ========================================================================== */

  .julia-quote {
    position: relative;
    background: linear-gradient(135deg, 
      rgba(var(--color-accent), 0.05) 0%, 
      rgba(var(--color-accent-gold), 0.05) 100%);
    border-left: 4px solid rgb(var(--color-accent));
    padding: 2rem;
    border-radius: 0; /* ENTERPRISE: Prostokątne quotes */
    margin: 2rem 0;
  }

  .julia-quote::before {
    content: '"';
    position: absolute;
    top: -1rem;
    left: 1rem;
    font-size: 4rem;
    color: rgba(var(--color-accent), 0.3);
    font-family: 'Playfair Display', serif;
    line-height: 1;
  }

  .julia-quote p {
    font-size: 1.125rem;
    font-style: italic;
    margin-bottom: 1rem;
    color: rgb(var(--color-primary));
  }

  .julia-quote cite {
    display: block;
    text-align: right;
    font-size: 0.875rem;
    color: rgb(var(--color-accent));
    font-weight: 500;
  }

  /* ==========================================================================
     COMMUNITY TEXT STYLING
     ========================================================================== */

  .community-text {
    background: linear-gradient(135deg, 
      rgba(var(--color-accent), 0.08) 0%, 
      rgba(var(--color-accent-gold), 0.08) 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    font-style: italic;
    color: rgb(var(--color-primary));
    margin: 2rem auto;
    max-width: 600px;
    position: relative;
  }

  .community-text::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, 
      rgb(var(--color-accent)), 
      rgb(var(--color-accent-gold)));
    border-radius: 0.75rem;
    z-index: -1;
    opacity: 0.3;
  }

  /* ==========================================================================
     EXCLUSIVITY BADGE
     ========================================================================== */

  .exclusivity-badge {
    background: linear-gradient(135deg, 
      rgba(var(--color-accent), 0.1) 0%, 
      rgba(var(--color-accent-gold), 0.1) 100%);
    border: 1px solid rgba(var(--color-accent), 0.2);
    border-radius: 0; /* ENTERPRISE: Prostokątne badges */
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(var(--color-accent));
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem auto;
  }

  /* ==========================================================================
     ENHANCED BUTTON STYLES - Spiritual & Luxury
     ========================================================================== */

  .btn-outline {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 0; /* ENTERPRISE: Prostokątne outline buttons */
    font-size: 0.875rem;
    font-weight: 300;
    letter-spacing: 0.05em;
    transition: all 0.3s var(--ease-smooth);
    backdrop-filter: blur(10px);
  }

  .btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }

  /* Enhanced primary button for spiritual theme */
  .btn-primary {
    background: linear-gradient(135deg, 
      rgb(var(--color-accent)) 0%, 
      rgb(var(--color-accent-gold)) 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 0; /* ENTERPRISE: Prostokątne primary buttons */
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.05em;
    transition: all 0.3s var(--ease-smooth);
    box-shadow: 0 4px 20px rgba(var(--color-accent), 0.3);
    position: relative;
    overflow: hidden;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(var(--color-accent), 0.4);
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(255, 255, 255, 0.2), 
      transparent);
    transition: left 0.5s;
  }

  .btn-primary:hover::before {
    left: 100%;
  }

  /* ==========================================================================
     PREMIUM CTA LINKS
     ========================================================================== */

  .premium-cta {
    color: rgb(var(--color-accent));
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s var(--ease-smooth);
    position: relative;
  }

  .premium-cta::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, 
      rgb(var(--color-accent)), 
      rgb(var(--color-accent-gold)));
    transition: width 0.3s var(--ease-smooth);
  }

  .premium-cta:hover::after {
    width: 100%;
  }

  .premium-cta:hover {
    color: rgb(var(--color-accent-gold));
    transform: translateX(4px);
  }

  /* ==========================================================================
     RESPONSIVE ENHANCEMENTS
     ========================================================================== */

  @media (max-width: 768px) {
    .hero-title {
      font-size: 48px;
      letter-spacing: 0.1em;
    }

    .hero-subtitle {
      font-size: 12px;
      letter-spacing: 0.2em;
    }

    .flow-section {
      padding: 80px 5%;
    }

    .premium-card {
      margin-bottom: 2rem;
    }

    .stats-section .grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 2rem;
    }

    .social-card {
      padding: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .hero-title {
      font-size: 36px;
    }

    .stats-section .grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .newsletter-section .form {
      flex-direction: column;
    }
  }

  /* ==========================================================================
     ACCESSIBILITY ENHANCEMENTS
     ========================================================================== */

  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Focus states for better accessibility */
  .btn-primary:focus,
  .btn-secondary:focus,
  .btn-outline:focus {
    outline: 2px solid rgb(var(--color-accent));
    outline-offset: 2px;
  }

  .premium-cta:focus {
    outline: 2px solid rgb(var(--color-accent));
    outline-offset: 4px;
    border-radius: 0; /* ENTERPRISE: Prostokątne focus states */
  }

  /* ==========================================================================
     LOADING STATES & MICRO-INTERACTIONS
     ========================================================================== */

  .loading-shimmer {
    background: linear-gradient(90deg, 
      rgba(var(--color-accent), 0.1) 25%, 
      rgba(var(--color-accent), 0.2) 50%, 
      rgba(var(--color-accent), 0.1) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  .retreat-card, .card {
    /* BEZ tła - NO background */
    background: transparent;
    /* BEZ ramek - NO borders */
    border: none;
    /* BEZ cieni - NO shadows */
    box-shadow: none;
    padding: 40px 0; /* zmniejszone z 60px do 40px */
    margin-bottom: 60px; /* zmniejszone z 120px do 60px */
    /* NIGDY: hover effects, color changes, visible containers */
    transition: none; /* USUNIĘTE wszystkie efekty - REMOVED all effects */
    overflow: visible;
  }

  .retreat-card:hover, .card:hover {
    /* NIGDY żadnych efektów hover - NEVER any hover effects */
    background: transparent;
    border: none;
    box-shadow: none;
    transform: none;
    opacity: 1;
  }

  .retreat-image {
    width: 100%;
    height: 500px; /* Increase from 400px for greater visual impact */
    object-fit: cover;
    /* ZERO zaokrągleń - ZERO border radius */
    border-radius: 0;
    /* Enhanced for aspirational photography */
    filter: brightness(1.05) contrast(0.95) saturate(1.1); /* Warm, inviting tone */
    display: block;
  }

  .retreat-info {
    margin-top: 30px;
    /* Tylko typografia - Only typography */
    background: transparent;
    padding: 0;
    border: none;
    box-shadow: none;
  }

  .card-content {
    padding: 2.5rem;
  }

  .card-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 16/9;
  }

  .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* ==========================================================================
     8. IMAGES - FULL WIDTH BREAKOUT (EDITORIAL STYLE)
     Images that break out of content flow for maximum impact
     ========================================================================== */

  .full-width-image {
    width: 100vw; /* full viewport width */
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    height: 60vh;
    object-fit: cover;
    margin-top: 100px;
    margin-bottom: 100px;
    border-radius: 0; /* everything must be rectangular */
    display: block;
  }

  /* ==========================================================================
     9. ABSOLUTE DESIGN PROHIBITIONS - ENFORCED
     Strict rules to maintain ultra-minimalist aesthetic
     ========================================================================== */

  /* Ensure no background colors on sections */
  section,
  .section,
  .flow-section,
  .retreat-section,
  .about-section,
  .calendar-section,
  .contact-section {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  /* Prohibit colored backgrounds on cards */
  .card,
  .retreat-card,
  .about-card,
  .calendar-card {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  /* Ensure all images are rectangular */
  img,
  .image,
  .retreat-image,
  .about-image,
  .hero-image {
    border-radius: 0 !important;
  }

  /* Prohibit bounce or pulse animations */
  @keyframes bounce {
    /* Disabled animation */
    0%, 100% { transform: none; }
  }

  @keyframes pulse {
    /* Disabled animation */
    0%, 100% { transform: none; }
  }

  .bounce,
  .pulse {
    animation: none !important;
  }

  /* ==========================================================================
     10. PAGE FLOW STRUCTURE - SEAMLESS TRANSITIONS
     Sections flow in exact order with minimal separators
     ========================================================================== */

  /* 1. HERO (100vh height) */
  .hero-section {
    height: 100vh;
    min-height: 100vh;
    /* Already defined above */
  }

  /* 2. RETREATS (images + text only) */
  .retreats-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  /* 3. ABOUT (image + adjacent text) */
  .about-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  /* 4. CALENDAR (ultra-simple grid) */
  .calendar-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  /* 5. CONTACT (centered text) */
  .contact-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
    text-align: center;
  }

  /* Thin divider lines between sections (60px x 1px subtle gray) */
  .section-divider-flow {
    width: 60px;
    height: 1px;
    background: #E5E2DD; /* subtle gray */
    margin: 60px auto; /* zmniejszone z 100px do 60px */
    border: none;
    /* No decorations - pure minimalism */
  }

  /* ==========================================================================
     DESIGN PHILOSOPHY ENFORCEMENT
     Single elegant document-like flow - Kinfolk/Cereal Magazine aesthetic
     ========================================================================== */

  /* Ensure seamless transitions between sections */
  .page-flow {
    width: 100%;
    background: #FEFDF8; /* warm white background only */
    /* No containers, no breaks, pure flow */
  }

  /* Typography as only decoration - reinforced */
  .editorial-text {
    font: 16px/1.8 'Helvetica Neue', 'Helvetica', sans-serif;
    font-weight: 300;
    max-width: 800px;
    margin: 0 auto;
    color: #2C2C2C;
    /* No backgrounds, borders, or decorations */
  }

  /* Ultra-simple calendar grid */
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 20px;
    max-width: 600px;
    margin: 0 auto;
    /* No backgrounds or borders */
  }

  .calendar-day {
    padding: 15px;
    text-align: center;
    background: transparent;
    border: none;
    font: 14px 'Helvetica Neue', sans-serif;
    color: #2C2C2C;
  }

  /* Centered contact text */
  .contact-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
  }

  /* White space as integral design element */
  .breathing-space {
    padding: 120px 0;
  }

  /* Images speak for themselves - no decorations */
  .editorial-image {
    width: 100%;
    height: auto;
    border-radius: 0;
    border: none;
    box-shadow: none;
    display: block;
    margin: 60px 0;
  }

  /* Professional spa/architecture magazine aesthetic */
  .magazine-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 80px;
    max-width: none;
    width: 100%;
  }

  @media (min-width: 1024px) {
    .magazine-layout {
      grid-template-columns: 1fr 1fr;
      gap: 120px;
    }
  }

  /* Ensure nothing visually "stands out" */
  .seamless-content {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
  }

  /* ==========================================================================
     TESTIMONIAL & JULIA'S PERSONAL BRANDING - Emotional Connection
     ========================================================================== */

  /* Elegant testimonial presentation */
  .testimonial-quote {
    font-size: 24px;
    font-style: italic;
    text-align: center;
    margin: 60px auto;
    max-width: 600px;
    color: rgb(var(--color-text-secondary));
    font-family: 'Cormorant Garamond', serif;
    line-height: 1.6;
  }

  .testimonial-author {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    color: rgb(var(--color-accent-gold));
    text-align: center;
    margin-top: 20px;
    font-weight: 300;
  }

  /* Julia's personal branding - warm and trustworthy */
  .julia-photo {
    filter: brightness(1.1) contrast(0.9) saturate(0.9);
    border-radius: 0; /* Keep rectangular for minimalist aesthetic */
  }

  .julia-quote {
    font-size: 24px;
    font-style: italic;
    text-align: center;
    margin: 60px auto;
    max-width: 600px;
    color: rgb(var(--color-text-secondary));
    font-family: 'Playfair Display', serif;
    line-height: 1.5;
  }

  /* Exclusivity indicators for female psychology */
  .exclusivity-badge {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    color: rgb(var(--color-accent-gold));
    background: transparent;
    border: 1px solid rgb(var(--color-accent-gold) / 0.3);
    padding: 8px 16px;
    display: inline-block;
    margin: 10px 0;
  }

  /* Community emphasis styling */
  .community-text {
    font-family: 'Cormorant Garamond', serif;
    font-size: 18px;
    font-style: italic;
    color: rgb(var(--color-text-secondary));
    text-align: center;
    margin: 30px auto;
  }
}

@layer utilities {
  /* ULTRA-PREMIUM LAYOUT - Lots of White Space */
  .section-padding {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  @media (min-width: 768px) {
    .section-padding {
      padding-top: 10rem;
      padding-bottom: 10rem;
    }
  }

  /* ZERO KONTENERÓW - usunięte ograniczenia szerokości */
  /* Zastąpione przez .section z padding: 120px 10% */
  .container-unified {
    /* DEPRECATED - używaj .section zamiast tego */
    max-width: none; /* brak ograniczeń - no limits */
    width: 100%; /* pełna szerokość - full width */
    margin: 0; /* bez marginesów - no margins */
    padding: 0 10%; /* tylko boczne oddychanie - only side breathing */
  }

  .section-unified-title {
    text-align: center;
    margin-bottom: 6rem;
  }

  .section-unified-title h2 {
    margin-bottom: 1.5rem;
    color: rgb(var(--color-accent));
  }

  .section-unified-title p {
    font-size: 1.125rem;
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.85);
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* PREMIUM GRID SYSTEM */
  .grid-12-col {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 768px) {
    .grid-12-col {
      gap: 2rem;
    }
  }

  .main-content {
    grid-column: span 12;
  }

  @media (min-width: 768px) {
    .main-content {
      grid-column: span 8;
    }
  }

  .sidebar {
    grid-column: span 12;
  }

  @media (min-width: 768px) {
    .sidebar {
      grid-column: span 4;
    }
  }

  /* ==========================================================================
     SEKCJE - PŁYNNE PRZEJŚCIA (FLOWING SECTION TRANSITIONS)
     Każda sekcja płynnie przechodzi w następną bez kontenerów
     ========================================================================== */

  .section {
    width: 100%; /* pełna szerokość - full width */
    padding: 180px 15%; /* Increase from 120px 10% for luxury feel */
    /* NIGDY osobnego tła - NEVER separate backgrounds */
    background: transparent;
    /* NIGDY ramek czy cieni - NEVER borders or shadows */
    border: none;
    box-shadow: none;
    /* BEZ widocznych kontenerów - NO visible containers */
    position: relative;
  }

  @media (min-width: 1024px) {
    .section {
      padding: 120px 10%; /* konsekwentne oddychanie - consistent breathing */
    }
  }

  .section-title {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-title h2 {
    margin-bottom: 1.5rem;
    color: rgb(var(--color-primary));
  }

  .section-title p {
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.125rem;
    color: rgb(var(--color-primary) / 0.7);
  }

  /* ==========================================================================
     SEPARATORY - MAKSYMALNIE SUBTELNE (MAXIMALLY SUBTLE SEPARATORS)
     Minimalne przejścia między sekcjami
     ========================================================================== */

  /* Separatory - MAKSYMALNIE subtelne - MAXIMALLY subtle separators */
  .section-divider {
    width: 60px;
    height: 1px;
    background: #E5E2DD; /* bardzo subtelny szary - very subtle gray */
    margin: 100px auto;
    border: none; /* NIGDY ramki - NEVER borders */
    /* TO WSZYSTKO - żadnych ozdób - THAT'S ALL - no decorations */
  }

  /* Zachowana kompatybilność z istniejącym kodem */
  .divider-line {
    width: 60px;
    height: 1px;
    background: #E5E2DD; /* bardzo subtelny szary - very subtle gray */
    margin: 100px auto;
    border: none;
  }

  .section-number {
    position: absolute;
    top: 2rem;
    left: 2rem;
    font-size: clamp(4rem, 8vw, 8rem);
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.02);
    font-family: var(--font-serif);
    line-height: 1;
    z-index: 0;
    pointer-events: none;
  }

  /* UTILITY CLASSES */
  .meta-text {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.6);
  }

  .subtitle {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    color: rgb(var(--color-accent) / 0.7);
    font-weight: 300;
  }

  /* RESPONSIVE DESIGN */
  @media (max-width: 768px) {
    .hero-title {
      font-size: clamp(36px, 8vw, 72px);
      letter-spacing: 0.15em;
    }

    .hero-subtitle {
      font-size: clamp(10px, 2vw, 14px);
      letter-spacing: 0.25em;
    }

    .hero-section {
      min-height: 85vh;
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .section-padding {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }

    .card-content {
      padding: 1.5rem;
    }

    .nav-content {
      padding: 1rem;
    }

    .container-unified {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  /* ACCESSIBILITY */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .fade-in {
      animation: none !important;
    }
  }

  /* PRINT STYLES */
  @media print {
    * {
      box-shadow: none !important;
      text-shadow: none !important;
      background: transparent !important;
      color: black !important;
    }

    .navbar,
    .btn-primary,
    .btn-secondary {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
      font-size: 12pt;
      line-height: 1.4;
    }

    a,
    a:visited {
      text-decoration: underline;
      color: #444 !important;
    }

    img {
      max-width: 100% !important;
      page-break-inside: avoid;
    }

    h2, h3 {
      page-break-after: avoid;
    }

    .card {
      border: 1px solid #ddd !important;
      page-break-inside: avoid;
    }
  }

  /* ==========================================================================
     FLOW IMPROVEMENTS - QUICK FIXES APPLIED
     ========================================================================== */

  /* Główne ulepszenia spacingu - zastępuje istniejące definicje */
  .flow-section, .section {
    padding: 80px 10% !important; /* zmniejszone z 120px */
  }

  /* Section dividers - cieńsze marginesy */
  .section-divider, .divider-line {
    margin: 60px auto !important; /* zmniejszone z 100px */
  }

  /* Testimoniale - lepszy układ */
  .testimonial-section {
    padding: 80px 10% !important;
    background: transparent !important;
  }

  .testimonial-slider {
    max-width: 800px;
    margin: 0 auto;
  }

  .testimonial-quote {
    font-size: 24px;
    line-height: 1.6;
    font-style: italic;
    color: #3A3432;
    margin-bottom: 30px;
  }

  /* Main container flow */
  .main-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }

  /* Usuń podwójne marginesy między sekcjami */
  .section + .section {
    margin-top: 0 !important;
  }

  /* Stopka z górną linią */
  footer {
    padding: 60px 10% 40px !important;
    margin-top: 80px !important;
    border-top: 1px solid #E5E2DD;
  }
}
