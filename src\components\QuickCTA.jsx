'use client';

import React from 'react';
import Link from 'next/link';
import { Calendar, MessageCircle, Phone } from 'lucide-react';

const QuickCTA = () => {
  return (
    <div className="fixed bottom-6 right-6 z-40 flex flex-col gap-3">
      {/* Floating CTA Cards */}
      <div className="bg-white shadow-lg border border-gray-200 rounded-lg p-4 max-w-xs transform hover:scale-105 transition-transform duration-300">
        <div className="text-center">
          <h3 className="font-serif text-lg text-black mb-2">
            Gotowa na Bali lub Sri Lankę?
          </h3>
          <p className="text-sm text-gray-600 mb-4 font-light">
            Zare<PERSON>wuj miejsce na transformacyjnym retreatie jogi
          </p>
          
          <div className="flex flex-col gap-2">
            <Link 
              href="/kontakt"
              className="w-full px-4 py-2 bg-black text-white text-sm font-light tracking-wide uppercase hover:opacity-90 transition-opacity text-center"
            >
              <Calendar className="inline w-4 h-4 mr-2" />
              Re<PERSON>wuj <PERSON>
            </Link>
            
            <a 
              href="https://wa.me/48123456789?text=Cześć! Interesuje mnie retreat jogi na Bali/Sri Lanka"
              target="_blank"
              rel="noopener noreferrer"
              className="w-full px-4 py-2 border border-green-500 text-green-600 text-sm font-light tracking-wide uppercase hover:bg-green-50 transition-colors text-center"
            >
              <MessageCircle className="inline w-4 h-4 mr-2" />
              WhatsApp
            </a>
          </div>
          
          <div className="mt-3 text-xs text-gray-500">
            🏝️ Bali • 🌿 Sri Lanka • 🧘‍♀️ Joga
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickCTA;