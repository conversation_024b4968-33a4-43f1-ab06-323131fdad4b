.blogContainer {
  padding: clamp(2rem, 5vw, 4rem) clamp(1rem, 3vw, 2rem);
  max-width: 1200px;
  margin: 0 auto;
}

.pageTitle {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 300;
  color: var(--color-temple);
  text-align: center;
  margin-bottom: 1rem;
  font-family: var(--font-display);
  letter-spacing: -0.01em;
}

.pageSubtitle {
  font-size: clamp(1rem, 2vw, 1.2rem);
  color: rgb(var(--color-wood-light) / 0.85);
  text-align: center;
  margin-bottom: clamp(2rem, 5vw, 3rem);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  font-weight: 300;
}

.postsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 320px), 1fr));
  gap: clamp(1.5rem, 3vw, 2.5rem);
}

.postCard {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--color-bamboo-light);
  will-change: transform;
  position: relative;
  height: 100%;
}

.postCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.postLink {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
  position: relative;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  background-color: var(--color-sand-light);
}

.imageContainer > div {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

.postImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
  will-change: transform;
}

.postCard:hover .postImage {
  transform: scale(1.05);
}

.postContent {
  padding: clamp(1rem, 2vw, 1.5rem);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.postTitle {
  font-size: clamp(1.2rem, 2vw, 1.4rem);
  font-weight: 600;
  color: var(--color-temple);
  margin-bottom: 0.75rem;
  font-family: var(--font-display);
  line-height: 1.3;
  transition: color 0.3s ease;
}

.postLink:hover .postTitle {
  color: var(--color-ocean);
}

.postExcerpt {
  font-size: clamp(0.95rem, 1.5vw, 1rem);
  color: var(--color-wood-light);
  line-height: 1.6;
  margin-bottom: 1rem;
  flex-grow: 1;
}

.postMeta {
  display: flex;
  align-items: center;
  gap: clamp(0.5rem, 1.5vw, 1rem);
  font-size: 0.875rem;
  color: var(--color-wood-light);
  margin-bottom: 1rem;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.tagsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: auto;
}

.tagBadge {
  font-size: 0.75rem;
  padding: 0.3rem 0.6rem;
  background-color: var(--color-bamboo-light);
  color: var(--color-wood-dark);
  border: none;
  border-radius: 4px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .postCard {
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (prefers-reduced-motion: reduce) {
  .postCard,
  .postImage,
  .postTitle {
    transition: none;
  }
  
  .postCard:hover {
    transform: none;
  }
  
  .postCard:hover .postImage {
    transform: none;
  }
}

@media (max-width: 480px) {
  .blogContainer {
    padding: 3rem 1rem;
  }
  .pageTitle {
    font-size: 2rem;
  }
  .pageSubtitle {
    font-size: 1rem;
  }
  .postTitle {
    font-size: 1.25rem;
  }
  .postExcerpt {
    font-size: 0.95rem;
  }
} 