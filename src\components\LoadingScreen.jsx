'use client';

import React from 'react';

const LoadingScreen = () => {
  return (
    <div className="fixed inset-0 bg-gradient-to-br from-amber-50 via-orange-50 to-stone-50 flex items-center justify-center z-50">
      <div className="text-center">
        {/* Mandala animowana */}
        <div className="mb-8">
          <div className="text-6xl text-amber-600/60 animate-spin" style={{ animationDuration: '3s' }}>
            🪷
          </div>
        </div>
        
        {/* Tekst loading */}
        <div className="text-amber-800 font-light text-lg tracking-wider">
          Przygotowujemy Twoje duchowe doświadczenie...
        </div>
        
        {/* Dots loading */}
        <div className="flex justify-center mt-6 space-x-2">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 bg-amber-600/40 rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.2}s` }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;