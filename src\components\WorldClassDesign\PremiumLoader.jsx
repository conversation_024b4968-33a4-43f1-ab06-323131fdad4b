'use client';

import React, { useEffect, useState } from 'react';

/**
 * ⌛ PREMIUM LOADER - TOP 1% DESIGN FEATURE
 * Elegancki loader z progresywnym ładowaniem
 * Inspirowane przez Apple, Linear.app, najlepsze SaaS
 */
const PremiumLoader = ({ 
  isLoading = true,
  progress = 0,
  showProgress = false,
  style = 'minimal',
  size = 'medium',
  color = '#7C9885',
  className = '',
  ...props 
}) => {
  const [displayProgress, setDisplayProgress] = useState(0);

  useEffect(() => {
    if (showProgress) {
      const interval = setInterval(() => {
        setDisplayProgress(prev => {
          const diff = progress - prev;
          if (Math.abs(diff) < 0.1) return progress;
          return prev + diff * 0.1;
        });
      }, 16);

      return () => clearInterval(interval);
    }
  }, [progress, showProgress]);

  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-8 h-8',
    large: 'w-12 h-12',
  };

  if (!isLoading) return null;

  // Minimal spinning loader
  const MinimalLoader = () => (
    <div
      className={`${sizeClasses[size]} ${className}`}
      style={{
        border: `2px solid transparent`,
        borderTop: `2px solid ${color}`,
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
      }}
      {...props}
    >
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );

  // Dots loader
  const DotsLoader = () => (
    <div className={`flex space-x-2 ${className}`} {...props}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className="w-2 h-2 rounded-full"
          style={{
            backgroundColor: color,
            animation: `pulse 1.4s ease-in-out ${i * 0.2}s infinite`,
          }}
        />
      ))}
      <style jsx>{`
        @keyframes pulse {
          0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
          40% { transform: scale(1); opacity: 1; }
        }
      `}</style>
    </div>
  );

  // Progress bar loader
  const ProgressLoader = () => (
    <div className={`w-full ${className}`} {...props}>
      <div
        className="h-1 bg-gray-200 rounded-full overflow-hidden"
        style={{ backgroundColor: `${color}20` }}
      >
        <div
          className="h-full bg-current transition-all duration-300 ease-out"
          style={{
            width: `${displayProgress}%`,
            backgroundColor: color,
          }}
        />
      </div>
      {showProgress && (
        <div
          className="text-xs text-center mt-2"
          style={{ color: color }}
        >
          {Math.round(displayProgress)}%
        </div>
      )}
    </div>
  );

  // Breathing loader
  const BreathingLoader = () => (
    <div
      className={`${sizeClasses[size]} ${className}`}
      style={{
        backgroundColor: color,
        borderRadius: '50%',
        animation: 'breathe 2s ease-in-out infinite',
      }}
      {...props}
    >
      <style jsx>{`
        @keyframes breathe {
          0%, 100% { transform: scale(1); opacity: 0.7; }
          50% { transform: scale(1.1); opacity: 1; }
        }
      `}</style>
    </div>
  );

  // Elegant loader with text
  const ElegantLoader = () => (
    <div className={`text-center ${className}`} {...props}>
      <div className="relative">
        <div
          className="w-16 h-16 mx-auto mb-4 rounded-full"
          style={{
            background: `conic-gradient(from 0deg, ${color}, ${color}40, ${color})`,
            animation: 'spin 2s linear infinite',
          }}
        >
          <div
            className="absolute inset-1 rounded-full"
            style={{ backgroundColor: 'white' }}
          />
        </div>
        <div
          className="text-sm font-light tracking-wide"
          style={{ color: color }}
        >
          Loading...
        </div>
      </div>
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );

  const loaders = {
    minimal: MinimalLoader,
    dots: DotsLoader,
    progress: ProgressLoader,
    breathing: BreathingLoader,
    elegant: ElegantLoader,
  };

  const LoaderComponent = loaders[style] || MinimalLoader;

  return (
    <div className="flex items-center justify-center">
      <LoaderComponent />
    </div>
  );
};

export default React.memo(PremiumLoader);