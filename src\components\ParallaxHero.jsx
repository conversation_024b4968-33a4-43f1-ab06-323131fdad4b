'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';

const ParallaxHero = ({
  children,
  imageUrl = '/images/background/bali-hero.webp',
  className = '',
  parallaxSpeed = 0.5
}) => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section className={`relative w-full h-screen flex items-center justify-center overflow-hidden ${className}`}>
      <div
        className="absolute inset-0 w-full h-full"
        style={{
          transform: `translateY(${scrollY * parallaxSpeed}px)`,
          willChange: 'transform'
        }}
      >
        <Image
          src={imageUrl}
          alt="Hero Background"
          fill
          priority
          className="object-cover scale-110"
          sizes="100vw"
          quality={90}
        />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-background/10" />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-8 sm:px-12 lg:px-16 text-center">
        <div data-aos="fade-up" data-aos-duration="1200" data-aos-delay="300">
          {children}
        </div>
      </div>
    </section>
  );
};

export default ParallaxHero;
