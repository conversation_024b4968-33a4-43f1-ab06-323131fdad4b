# Bakasana Travel Blog Information

## Summary
A Next.js-based website for yoga retreats in Bali and Sri Lanka led by <PERSON>, a physiotherapist and certified yoga instructor. The site showcases transformational yoga retreats, combining yoga practice with exploring beautiful locations in Bali and Sri Lanka.

## Structure
- **src/app**: Next.js App Router structure with pages and routes
- **src/components**: React components organized by functionality
- **src/data**: Static data for blog posts, programs, and navigation
- **src/lib**: Utility functions and structured data generators
- **src/hooks**: Custom React hooks
- **public**: Static assets including images and manifest
- **sanity**: Sanity CMS schemas and configuration

## Language & Runtime
**Language**: JavaScript/React with JSX
**Version**: Next.js 15.x
**Build System**: Next.js build system
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- Next.js 15.3.2 (React framework)
- React 18.3.1
- Tailwind CSS 3.4.17 (Styling)
- Framer Motion 12.12.2 (Animations)
- Sanity CMS (Content management)
- Mapbox GL 3.13.0 (Interactive maps)
- Vercel Analytics/Speed Insights (Performance monitoring)

**Development Dependencies**:
- ESLint 9.27.0
- TypeScript 5.4.5
- PostCSS 8.5.3
- Bundle Analyzer

## Build & Installation
```bash
# Install dependencies
npm install

# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start

# Analyze bundle
npm run build:analyze

# Sanity CMS development
npm run sanity:dev
```

## Content Management
**CMS**: Sanity
**Configuration**: sanity.config.js
**Schemas**: 
- Blog posts
- Retreats
- Testimonials
- FAQ
- Authors
- Categories
- Site settings

## Performance Optimization
**Image Optimization**: Sharp, Next.js Image component
**CSS Optimization**: Tailwind with PostCSS
**Bundle Optimization**: Code splitting, tree shaking
**Caching Strategy**: Long-term caching for static assets
**Web Vitals Monitoring**: Vercel Speed Insights

## SEO Features
**Metadata**: Dynamic metadata generation
**Structured Data**: JSON-LD for rich snippets
**Sitemap**: Auto-generated with next-sitemap
**Performance Score**: 95+ Lighthouse score

## UI Framework
**Design System**: Custom Tailwind-based design system
**Color Scheme**: Bali-inspired palette (temple, golden, sage, lotus, mist)
**Typography**: Playfair Display (serif) for headings, Inter (sans) for body
**Components**: Custom UI components with subtle animations
**Responsive Design**: Mobile-first approach with multiple breakpoints

## Key Features
- Interactive retreat calendar
- Booking form with validation
- Blog with CMS integration
- Image gallery with optimization
- Interactive map for locations
- Newsletter signup
- PWA support
- Analytics integration