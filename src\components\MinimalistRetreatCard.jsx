import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

/**
 * MinimalistRetreatCard - KARTY RETREATÓW - MINIMALIZM
 * 
 * Implementuje zasady ultra-minimalizmu:
 * - BEZ tła, ramek, cieni
 * - ZERO visual decorations (emojis, icons, badges)
 * - Tylko typografia jako oz<PERSON>
 * - Sharp-edged editorial photography
 * - Transparent backgrounds
 */
export default function MinimalistRetreatCard({ 
  retreat,
  className = ''
}) {
  if (!retreat) return null;

  return (
    <article className={`retreat-card ${className}`}>
      {/* SHARP EDITORIAL IMAGE - ZERO border radius */}
      <div className="retreat-image-container">
        <Image
          src={retreat.imageUrl || retreat.image || '/images/background/bali-hero.webp'}
          alt={retreat.title || 'Retreat'}
          width={800}
          height={400}
          className="retreat-image"
          priority={false}
          quality={90}
        />
      </div>

      {/* PURE TYPOGRAPHY - NO decorations */}
      <div className="retreat-info">
        {/* Tytuł - Didot serif */}
        <h3 style={{
          font: '24px "Didot", "Bodoni MT", "Times New Roman", serif',
          fontWeight: 300,
          color: '#2C2C2C',
          marginBottom: '15px',
          letterSpacing: '0.03em',
          lineHeight: 1.2,
          textAlign: 'left'
        }}>
          {retreat.title}
        </h3>

        {/* Opis - Helvetica Neue */}
        <p style={{
          font: '16px/1.8 "Helvetica Neue", "Helvetica", sans-serif',
          fontWeight: 300,
          color: '#2C2C2C',
          marginBottom: '20px',
          maxWidth: '800px'
        }}>
          {retreat.description || retreat.shortDescription}
        </p>

        {/* Daty - TYLKO tekst, BEZ ikon */}
        {(retreat.startDate || retreat.endDate) && (
          <p style={{
            font: '14px "Helvetica Neue", "Helvetica", sans-serif',
            fontWeight: 300,
            color: '#6B6B6B',
            marginBottom: '10px',
            textTransform: 'uppercase',
            letterSpacing: '0.1em'
          }}>
            {retreat.startDate} {retreat.endDate && `- ${retreat.endDate}`}
          </p>
        )}

        {/* Lokalizacja - TYLKO tekst, BEZ ikon */}
        {retreat.location && (
          <p style={{
            font: '14px "Helvetica Neue", "Helvetica", sans-serif',
            fontWeight: 300,
            color: '#6B6B6B',
            marginBottom: '20px',
            textTransform: 'uppercase',
            letterSpacing: '0.1em'
          }}>
            {retreat.location}
          </p>
        )}

        {/* Link - MINIMALISTYCZNY, BEZ przycisku */}
        {retreat.link && (
          <Link 
            href={retreat.link}
            style={{
              font: '14px "Helvetica Neue", "Helvetica", sans-serif',
              fontWeight: 300,
              color: '#2C2C2C',
              textDecoration: 'underline',
              textDecorationThickness: '1px',
              textUnderlineOffset: '4px',
              letterSpacing: '0.05em'
            }}
          >
            Zobacz szczegóły
          </Link>
        )}
      </div>
    </article>
  );
}

/**
 * MinimalistRetreatGrid - Siatka kart bez kontenerów
 */
export function MinimalistRetreatGrid({ retreats, className = '' }) {
  if (!retreats || retreats.length === 0) return null;

  return (
    <div className={`section ${className}`}>
      {retreats.map((retreat, index) => (
        <MinimalistRetreatCard 
          key={retreat.id || retreat._id || index}
          retreat={retreat}
        />
      ))}
    </div>
  );
}

/**
 * Wariant z pojedynczą kartą na pełną szerokość
 */
export function MinimalistRetreatFull({ retreat, className = '' }) {
  if (!retreat) return null;

  return (
    <div className={`section ${className}`}>
      <MinimalistRetreatCard retreat={retreat} />
    </div>
  );
}
