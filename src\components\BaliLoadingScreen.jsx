'use client';

import React from 'react';

const BaliLoadingScreen = ({ isLoading = true }) => {
  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 z-50 flex items-center justify-center">
      {/* Tło z subtelną teksturą */}
      <div className="absolute inset-0 texture-bali opacity-30"></div>
      
      {/* Główna mandala */}
      <div className="relative z-10 text-center">
        {/* Animowana mandala */}
        <div className="relative mb-8">
          {/* Zewnętrzny krąg */}
          <div className="w-32 h-32 mx-auto relative">
            <div className="absolute inset-0 border-4 border-amber-300/30 rounded-full animate-spin" style={{ animationDuration: '8s' }}></div>
            <div className="absolute inset-2 border-2 border-amber-400/40 rounded-full animate-spin" style={{ animationDuration: '6s', animationDirection: 'reverse' }}></div>
            <div className="absolute inset-4 border border-amber-500/50 rounded-full animate-spin" style={{ animationDuration: '4s' }}></div>
            
            {/* Centralny symbol */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-4xl text-amber-600 lotus-pulse">🪷</span>
            </div>
          </div>
          
          {/* Płatki wokół mandali */}
          <div className="absolute inset-0 flex items-center justify-center">
            {[0, 45, 90, 135, 180, 225, 270, 315].map((rotation, index) => (
              <div
                key={index}
                className="absolute w-6 h-6 text-amber-500/40"
                style={{
                  transform: `rotate(${rotation}deg) translateY(-60px)`,
                  animation: `gentleFloat 3s ease-in-out infinite`,
                  animationDelay: `${index * 0.2}s`
                }}
              >
                ❀
              </div>
            ))}
          </div>
        </div>
        
        {/* Tekst ładowania */}
        <div className="space-y-4">
          <h2 className="text-2xl font-light text-amber-700 tracking-wide" style={{ fontFamily: 'Cormorant Garamond, serif' }}>
            Przygotowujemy Twoją podróż...
          </h2>
          
          {/* Balijskie pozdrowienie */}
          <p className="text-amber-600/80 italic text-lg" style={{ fontFamily: 'Georgia, serif' }}>
            Om Swastiastu
          </p>
          
          {/* Animowane kropki */}
          <div className="flex justify-center space-x-2 mt-6">
            {[0, 1, 2].map((index) => (
              <div
                key={index}
                className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"
                style={{ animationDelay: `${index * 0.3}s` }}
              ></div>
            ))}
          </div>
        </div>
        
        {/* Subtelne cytaty */}
        <div className="mt-12 max-w-md mx-auto">
          <p className="text-sm text-amber-600/60 font-light italic leading-relaxed">
            "Każda podróż zaczyna się od jednego kroku, każda praktyka od jednego oddechu"
          </p>
        </div>
      </div>
      
      {/* Spadające płatki w tle */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(5)].map((_, index) => (
          <div
            key={index}
            className="absolute text-2xl text-amber-400/20"
            style={{
              left: `${20 + index * 15}%`,
              animation: `fallAndRotate ${8 + index * 2}s linear infinite`,
              animationDelay: `${index * 1.5}s`
            }}
          >
            🌸
          </div>
        ))}
      </div>
    </div>
  );
};

export default BaliLoadingScreen;
