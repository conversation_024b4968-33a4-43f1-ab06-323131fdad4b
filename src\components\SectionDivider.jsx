import React from 'react';

/**
 * SectionDivider - MAKSYMALNIE SUBTELNY SEPARATOR
 * 
 * Implementuje zasady płynnych przejść między sekcjami:
 * - 60px szerokości, 1px wysokości
 * - Bar<PERSON>zo subtelny szary kolor (#E5E2DD)
 * - 100px margines góra/dół
 * - ŻADNYCH ozdób - TO WSZYSTKO
 */
export default function SectionDivider({ className = '' }) {
  return (
    <div 
      className={`section-divider ${className}`}
      role="separator"
      aria-hidden="true"
    />
  );
}

/**
 * Wariant z customowym marginesem
 */
export function SectionDividerCustom({ 
  marginTop = '100px', 
  marginBottom = '100px',
  className = '' 
}) {
  return (
    <div 
      className={`section-divider ${className}`}
      style={{
        marginTop,
        marginBottom
      }}
      role="separator"
      aria-hidden="true"
    />
  );
}

/**
 * Wariant niewidoczny - tylko dla przestrzeni
 */
export function SectionSpacer({ height = '100px', className = '' }) {
  return (
    <div 
      className={className}
      style={{ height }}
      aria-hidden="true"
    />
  );
}
