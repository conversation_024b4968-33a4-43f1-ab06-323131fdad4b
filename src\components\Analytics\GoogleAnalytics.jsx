"use client"; // Ta dyrektywa jest kluczowa dla komponentów klienckich

import Script from 'next/script';
// The 'useEffect' import was here but not used directly in this component.
// It's good practice to remove unused imports.

const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

export default function GoogleAnalytics() {
  if (!GA_TRACKING_ID) {
    // It's helpful to log a warning if the ID is missing, for easier debugging.
    console.warn('Google Analytics Measurement ID (NEXT_PUBLIC_GA_MEASUREMENT_ID) is not set. Google Analytics will not be initialized.');
    return null;
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
        onLoad={() => {
          console.log('GTag script (gtag.js) loaded successfully.');
        }}
        onError={(e) => {
          console.error('Error loading GTag script (gtag.js):', e);
        }}
      />
      <Script
        id="google-analytics-config-script" // Using a more unique ID for this inline script
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_path: window.location.pathname,
            });
            console.log('Google Analytics configured with ID:', '${GA_TRACKING_ID}');
          `,
        }}
      />
    </>
  );
}