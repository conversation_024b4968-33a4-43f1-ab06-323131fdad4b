@tailwind base;
@tailwind components;
@tailwind utilities;

/* ULTRA-MINIMALIST PROFESSIONAL GLOBALS */

:root {
  --black: #000000;
  --charcoal: #2C2C2C;
  --gray: #666666;
  --light-gray: #E5E5E5;
  --white: #FFFFFF;
  --warm-white: #FAFAF8;
}

/* GLOBAL RULES FOR ULTRA-MINIMALISM */
* {
  box-sizing: border-box;
  border-radius: 0 !important; /* Zero rounded corners globally */
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
}

body {
  font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--warm-white);
  overflow-x: hidden;
}

/* TYPOGRAPHY HIERARCHY */
h1, h2, h3 {
  font-family: 'Didot', 'Bodoni MT', 'Playfair Display', serif;
  font-weight: 400;
  letter-spacing: 0.02em;
  line-height: 1.2;
  color: var(--black);
}

h1 {
  font-size: 4rem;
  letter-spacing: 0.2em;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: 1.5rem;
  font-weight: 300;
}

a {
  color: inherit;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

a:hover {
  opacity: 0.7; /* Only opacity changes on hover */
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  width: 100%;
}

/* PROFESSIONAL LAYOUT */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 120px 10%; /* Generous breathing space */
}

.section {
  padding: 120px 0;
}

/* GHOST BUTTONS - ULTRA MINIMAL */
.btn {
  padding: 15px 40px;
  border: 1px solid currentColor;
  background: transparent;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 12px;
  font-family: 'Helvetica Neue', sans-serif;
  font-weight: 300;
  transition: opacity 0.2s ease;
  cursor: pointer;
  display: inline-block;
}

.btn:hover {
  opacity: 0.7;
}

/* SECTION DIVIDERS - MINIMAL */
.section-divider {
  width: 60px;
  height: 1px;
  background: var(--light-gray);
  margin: 0 auto;
}

/* UTILITIES */
.text-center { text-align: center; }

/* REMOVE ALL ANIMATIONS, SHADOWS, GRADIENTS */
.animate-pulse,
.animate-bounce,
.animate-spin,
.shadow,
.shadow-sm,
.shadow-md,
.shadow-lg,
.shadow-xl,
.backdrop-blur,
.backdrop-blur-sm,
.backdrop-blur-md,
.bg-gradient-to-r,
.bg-gradient-to-b,
.bg-gradient-to-t,
.bg-gradient-to-l {
  animation: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
  background: transparent !important;
}

/* MINIMAL SCROLLBAR */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--sage);
  opacity: 0.3;
}

/* BALI & SRI LANKA THEMED STYLES */
:root {
  --bali-temple: #D4AF37;
  --bali-sage: #87A96B;
  --bali-lotus: #E8DDB5;
  --sri-lanka-emerald: #50B83C;
  --sri-lanka-saffron: #FF9933;
  --sri-lanka-maroon: #800020;
  --ocean-blue: #006994;
  --sunset-orange: #FF6B35;
}

/* Destination-specific accents */
.bali-accent {
  color: var(--bali-temple);
}

.sri-lanka-accent {
  color: var(--sri-lanka-emerald);
}

/* Yoga-specific styling */
.yoga-practice {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.05) 0%, rgba(135, 169, 107, 0.05) 100%);
}

/* Retreat cards hover effects */
.retreat-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.retreat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Destination badges */
.destination-badge {
  background: var(--bali-lotus);
  color: var(--charcoal);
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.destination-badge.sri-lanka {
  background: rgba(80, 184, 60, 0.1);
  color: var(--sri-lanka-emerald);
}

/* Testimonial styling with cultural touch */
.testimonial-quote {
  position: relative;
  font-style: italic;
  color: var(--charcoal);
}

.testimonial-quote::before {
  content: '"';
  font-size: 3rem;
  color: var(--bali-sage);
  position: absolute;
  top: -1rem;
  left: -1rem;
  font-family: serif;
}

/* Enhanced section spacing for better visual hierarchy */
.wellness-section {
  padding: 6rem 0;
}

@media (max-width: 768px) {
  .wellness-section {
    padding: 4rem 0;
  }
}