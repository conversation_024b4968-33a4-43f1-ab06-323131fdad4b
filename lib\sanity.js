// lib/sanity.js
import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'

// Konfiguracja klienta Sanity
export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'your-project-id', // Zastąp swoim Project ID
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  apiVersion: '2024-01-01',
  useCdn: process.env.NODE_ENV === 'production', // Użyj CDN w produkcji
  token: process.env.SANITY_API_TOKEN, // Tylko dla operacji zapisu
})

// Builder dla obrazków
const builder = imageUrlBuilder(client)

// Helper do generowania URL obrazków
export const urlFor = (source) => {
  if (!source) return null
  return builder.image(source)
}

// Funkcje do pobierania danych

// Pobierz wszystkie wyjazdy/retreaty
export async function getRetreats() {
  try {
    const retreats = await client.fetch(`
      *[_type == "retreat"] | order(startDate asc) {
        _id,
        title,
        slug,
        description,
        shortDescription,
        startDate,
        endDate,
        price,
        currency,
        maxParticipants,
        currentParticipants,
        location,
        highlights,
        included,
        notIncluded,
        images[] {
          asset,
          alt,
          caption
        },
        featured,
        status,
        _createdAt,
        _updatedAt
      }
    `)
    return retreats
  } catch (error) {
    console.error('Error fetching retreats:', error)
    return []
  }
}

// Pobierz pojedynczy retreat po slug
export async function getRetreatBySlug(slug) {
  try {
    const retreat = await client.fetch(`
      *[_type == "retreat" && slug.current == $slug][0] {
        _id,
        title,
        slug,
        description,
        shortDescription,
        startDate,
        endDate,
        price,
        currency,
        maxParticipants,
        currentParticipants,
        location,
        highlights,
        included,
        notIncluded,
        images[] {
          asset,
          alt,
          caption
        },
        featured,
        status,
        _createdAt,
        _updatedAt
      }
    `, { slug })
    return retreat
  } catch (error) {
    console.error('Error fetching retreat:', error)
    return null
  }
}

// Pobierz wszystkie opinie
export async function getTestimonials() {
  try {
    const testimonials = await client.fetch(`
      *[_type == "testimonial"] | order(_createdAt desc) {
        _id,
        name,
        photo {
          asset,
          alt
        },
        content,
        rating,
        retreatDate,
        location,
        featured,
        _createdAt
      }
    `)
    return testimonials
  } catch (error) {
    console.error('Error fetching testimonials:', error)
    return []
  }
}

// Pobierz wyróżnione opinie
export async function getFeaturedTestimonials() {
  try {
    const testimonials = await client.fetch(`
      *[_type == "testimonial" && featured == true] | order(_createdAt desc) [0...6] {
        _id,
        name,
        photo {
          asset,
          alt
        },
        content,
        rating,
        retreatDate,
        location,
        _createdAt
      }
    `)
    return testimonials
  } catch (error) {
    console.error('Error fetching featured testimonials:', error)
    return []
  }
}

// Pobierz wszystkie FAQ
export async function getFAQs() {
  try {
    const faqs = await client.fetch(`
      *[_type == "faq"] | order(order asc) {
        _id,
        question,
        answer,
        category,
        order,
        featured
      }
    `)
    return faqs
  } catch (error) {
    console.error('Error fetching FAQs:', error)
    return []
  }
}

// Pobierz artykuły bloga
export async function getBlogPosts() {
  try {
    const posts = await client.fetch(`
      *[_type == "blogPost"] | order(publishedAt desc) {
        _id,
        title,
        slug,
        excerpt,
        content,
        mainImage {
          asset,
          alt
        },
        author-> {
          name,
          image
        },
        categories[]-> {
          title,
          slug
        },
        tags,
        publishedAt,
        featured,
        seo {
          metaTitle,
          metaDescription,
          keywords
        }
      }
    `)
    return posts
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}

// Pobierz pojedynczy post bloga
export async function getBlogPostBySlug(slug) {
  try {
    const post = await client.fetch(`
      *[_type == "blogPost" && slug.current == $slug][0] {
        _id,
        title,
        slug,
        excerpt,
        content,
        mainImage {
          asset,
          alt
        },
        author-> {
          name,
          image,
          bio
        },
        categories[]-> {
          title,
          slug
        },
        tags,
        publishedAt,
        featured,
        seo {
          metaTitle,
          metaDescription,
          keywords
        }
      }
    `, { slug })
    return post
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return null
  }
}

// Pobierz ustawienia strony
export async function getSiteSettings() {
  try {
    const settings = await client.fetch(`
      *[_type == "siteSettings"][0] {
        title,
        description,
        keywords,
        logo {
          asset,
          alt
        },
        socialMedia {
          facebook,
          instagram,
          youtube,
          whatsapp
        },
        contact {
          email,
          phone,
          address
        },
        seo {
          metaTitle,
          metaDescription,
          ogImage {
            asset,
            alt
          }
        }
      }
    `)
    return settings
  } catch (error) {
    console.error('Error fetching site settings:', error)
    return null
  }
}

// Helper do formatowania dat
export function formatDate(dateString) {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  return date.toLocaleDateString('pl-PL', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Helper do formatowania cen
export function formatPrice(price, currency = 'PLN') {
  if (!price) return ''
  
  return new Intl.NumberFormat('pl-PL', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price)
}
