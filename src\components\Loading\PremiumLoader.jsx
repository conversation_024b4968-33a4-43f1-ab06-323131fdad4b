'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';

const PremiumLoader = ({ isLoading = true, onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState('loading'); // loading, completing, complete

  useEffect(() => {
    if (!isLoading) return;

    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          setStage('completing');
          setTimeout(() => {
            setStage('complete');
            onComplete?.();
          }, 500);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 100);

    return () => clearInterval(timer);
  }, [isLoading, onComplete]);

  const containerVariants = {
    initial: { opacity: 1 },
    exit: { 
      opacity: 0,
      transition: { 
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const logoVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const progressVariants = {
    initial: { width: '0%' },
    animate: { 
      width: `${progress}%`,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  };

  return (
    <AnimatePresence>
      {stage !== 'complete' && (
        <motion.div
          className="fixed inset-0 z-50 bg-gradient-to-br from-primary via-primary/95 to-primary flex items-center justify-center"
          variants={containerVariants}
          initial="initial"
          exit="exit"
        >
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px)`,
              backgroundSize: '60px 60px'
            }} />
          </div>

          <div className="relative text-center">
            {/* Logo */}
            <motion.div
              variants={logoVariants}
              initial="initial"
              animate="animate"
              className="mb-12"
            >
              <h1 className="text-6xl md:text-8xl font-serif font-light text-white tracking-[0.2em] mb-4">
                BAKASANA
              </h1>
              <p className="text-white/70 text-sm tracking-[0.3em] uppercase font-light">
                Retreat & Wellness Sanctuary
              </p>
            </motion.div>

            {/* Progress bar */}
            <div className="w-80 mx-auto mb-8">
              <div className="h-px bg-white/20 relative overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-accent to-gold"
                  variants={progressVariants}
                  initial="initial"
                  animate="animate"
                />
              </div>
              <div className="flex justify-between items-center mt-4">
                <span className="text-white/50 text-xs tracking-wider">
                  {Math.round(progress)}%
                </span>
                <span className="text-white/50 text-xs tracking-wider">
                  {stage === 'loading' ? 'Ładowanie sanktuarium...' : 'Gotowe'}
                </span>
              </div>
            </div>

            {/* Loading animation */}
            <motion.div 
              className="flex items-center justify-center space-x-2"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-white/40 rounded-full"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PremiumLoader;