'use client';

import { useState, useEffect } from 'react';
import { mainNavItems } from '@/data/navigationLinks';

export default function ClientNavbar() {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav className={`fixed w-full z-50 ${
      scrolled ? 'bg-white' : 'bg-transparent'
    }`}>
      <div className="container mx-auto flex justify-between items-center py-6 px-4">
        <a href="/" className="text-2xl font-serif text-gray-800">
          BAKASANA
        </a>
        <div className="flex gap-8">
          {mainNavItems.map(item => (
            <a
              key={item.href}
              href={item.href}
              className="text-gray-800 hover:opacity-70 transition-opacity duration-200"
            >
              {item.label}
            </a>
          ))}
        </div>
      </div>
    </nav>
  );
}