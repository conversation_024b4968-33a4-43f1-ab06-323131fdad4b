# Hero Section - Warianty Minimalistyczne

## Obecny Stan
Hero Section został przeprojektowany na ultra minimalistyczny design inspirowany:
- Apple.com
- Squarespace templates
- Luksusowe hotele (Four Seasons, Aman)

## Dostępne Opcje

### 1. Overlay na Obrazie
**Aktualnie aktywny:** Czarny gradient od góry
```css
background: linear-gradient(180deg, rgba(0,0,0,0.2) 0%, transparent 40%)
```

**Opcja 2:** Delikatny biały overlay
```css
background: rgba(255, 255, 255, 0.1)
```

**Opcja 3:** <PERSON><PERSON>zo subtelny czarny
```css
background: rgba(0, 0, 0, 0.15)
```

### 2. Przycisk
**Aktualnie:** Widoczny minimalistyczny przycisk outline
- Można zakomentować dla ultra minimalizmu
- Prostokątny (border-radius: 0)
- Tylko biały border bez wypełnienia

### 3. Tekst
- Główny tytuł: `font-weight: 200` (ultra cienki)
- Podtytuł: `font-weight: 300, opacity: 0.8`
- <PERSON><PERSON><PERSON> "pływa" bezpośrednio na obrazie bez kontenera

### 4. Obraz
- Delikatne filtry: `contrast(0.9) brightness(1.1)`
- Opacity: `0.9`
- Pełny ekran: `min-height: 100vh`

## Jak Zmienić Wariant

### Aby zmienić overlay:
1. Zakomentuj aktualny overlay (linia 43-47)
2. Odkomentuj wybrany wariant (linia 49-64)

### Aby usunąć przycisk:
1. Zakomentuj cały blok przycisku (linia 103-126)

### Aby zmienić obraz:
1. Zmień ścieżkę w `src="/images/background/bali-hero.webp"`
2. Dostosuj filtry w `style={{ filter: '...', opacity: '...' }}`

## Efekt Końcowy
- Obraz jest gwiazdą
- Tekst delikatnie dopełnia, nie dominuje
- Minimalistyczny design bez zbędnych elementów
- Fokus na estetyce i przestrzeni