'use client';

import React, { useEffect, useState } from 'react';

/**
 * 🎭 PREMIUM TRANSITIONS - TOP 1% DESIGN FEATURE
 * <PERSON><PERSON><PERSON>e przej<PERSON>cia między stronami jak w Apple/Linear.app
 * Inspirowane przez najlepsze SPA transitions
 */

// Page transition wrapper
export const PageTransition = ({ 
  children, 
  isLoading = false,
  transition = 'fade',
  duration = 300,
  className = '',
  ...props 
}) => {
  const [isVisible, setIsVisible] = useState(!isLoading);

  useEffect(() => {
    if (!isLoading) {
      const timer = setTimeout(() => setIsVisible(true), 50);
      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [isLoading]);

  const transitions = {
    fade: {
      opacity: isVisible ? 1 : 0,
      transition: `opacity ${duration}ms cubic-bezier(0.22, 1, 0.36, 1)`,
    },
    slideUp: {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
      transition: `all ${duration}ms cubic-bezier(0.22, 1, 0.36, 1)`,
    },
    slideDown: {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateY(0)' : 'translateY(-20px)',
      transition: `all ${duration}ms cubic-bezier(0.22, 1, 0.36, 1)`,
    },
    scale: {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'scale(1)' : 'scale(0.95)',
      transition: `all ${duration}ms cubic-bezier(0.22, 1, 0.36, 1)`,
    },
    blur: {
      opacity: isVisible ? 1 : 0,
      filter: isVisible ? 'blur(0px)' : 'blur(4px)',
      transition: `all ${duration}ms cubic-bezier(0.22, 1, 0.36, 1)`,
    },
  };

  return (
    <div
      className={`${className}`}
      style={{
        ...transitions[transition],
        willChange: 'transform, opacity, filter',
      }}
      {...props}
    >
      {children}
    </div>
  );
};

// Smooth reveal with intersection observer
export const SmoothReveal = ({ 
  children, 
  delay = 0,
  duration = 600,
  offset = 30,
  threshold = 0.1,
  triggerOnce = true,
  className = '',
  ...props 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const [ref, setRef] = useState(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && (!triggerOnce || !hasTriggered)) {
          setTimeout(() => {
            setIsVisible(true);
            setHasTriggered(true);
          }, delay);
        } else if (!triggerOnce && !entry.isIntersecting) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin: '50px',
      }
    );

    observer.observe(ref);

    return () => observer.disconnect();
  }, [ref, delay, threshold, triggerOnce, hasTriggered]);

  return (
    <div
      ref={setRef}
      className={`${className}`}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateY(0)' : `translateY(${offset}px)`,
        transition: `opacity ${duration}ms cubic-bezier(0.22, 1, 0.36, 1) ${delay}ms, transform ${duration}ms cubic-bezier(0.22, 1, 0.36, 1) ${delay}ms`,
        willChange: 'transform, opacity',
      }}
      {...props}
    >
      {children}
    </div>
  );
};

// Staggered children animation
export const StaggeredChildren = ({ 
  children, 
  delay = 50,
  duration = 400,
  className = '',
  ...props 
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`${className}`} {...props}>
      {React.Children.map(children, (child, index) => (
        <div
          key={index}
          style={{
            opacity: isVisible ? 1 : 0,
            transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
            transition: `all ${duration}ms cubic-bezier(0.22, 1, 0.36, 1)`,
            transitionDelay: `${index * delay}ms`,
            willChange: 'transform, opacity',
          }}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

// Morphing background transition
export const MorphingBackground = ({ 
  colors = ['#F5F5F0', '#E8E8E3', '#DCDCD7'],
  duration = 8000,
  className = '',
  ...props 
}) => {
  const [currentColor, setCurrentColor] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentColor(prev => (prev + 1) % colors.length);
    }, duration);

    return () => clearInterval(interval);
  }, [colors.length, duration]);

  return (
    <div
      className={`absolute inset-0 ${className}`}
      style={{
        background: `linear-gradient(45deg, ${colors[currentColor]}, ${colors[(currentColor + 1) % colors.length]})`,
        transition: `background ${duration / 2}ms ease-in-out`,
        willChange: 'background',
      }}
      {...props}
    />
  );
};

// Parallax scroll effect
export const ParallaxElement = ({ 
  children, 
  speed = 0.5,
  className = '',
  ...props 
}) => {
  const [offset, setOffset] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      setOffset(scrolled * speed);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return (
    <div
      className={`${className}`}
      style={{
        transform: `translateY(${offset}px)`,
        willChange: 'transform',
      }}
      {...props}
    >
      {children}
    </div>
  );
};

// Magnetic hover effect
export const MagneticHover = ({ 
  children, 
  strength = 0.2,
  className = '',
  ...props 
}) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  const handleMouseMove = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;
    
    setPosition({ x: x * strength, y: y * strength });
  };

  const handleMouseLeave = () => {
    setPosition({ x: 0, y: 0 });
    setIsHovering(false);
  };

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  return (
    <div
      className={`${className}`}
      style={{
        transform: `translate(${position.x}px, ${position.y}px)`,
        transition: isHovering ? 'none' : 'transform 0.3s cubic-bezier(0.22, 1, 0.36, 1)',
        willChange: 'transform',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
      {...props}
    >
      {children}
    </div>
  );
};

// Breathing animation
export const BreathingElement = ({ 
  children, 
  intensity = 0.02,
  duration = 4000,
  className = '',
  ...props 
}) => {
  return (
    <div
      className={`${className}`}
      style={{
        animation: `breathe ${duration}ms ease-in-out infinite`,
        willChange: 'transform',
      }}
      {...props}
    >
      {children}
      <style jsx>{`
        @keyframes breathe {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(${1 + intensity});
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          div {
            animation: none !important;
          }
        }
      `}</style>
    </div>
  );
};

const PremiumTransitions = {
  PageTransition,
  SmoothReveal,
  StaggeredChildren,
  MorphingBackground,
  ParallaxElement,
  MagneticHover,
  BreathingElement,
};

export default PremiumTransitions;