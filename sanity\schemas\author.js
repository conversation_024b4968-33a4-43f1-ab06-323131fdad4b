// sanity/schemas/author.js
export default {
  name: 'author',
  title: 'Autorzy',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: '<PERSON><PERSON><PERSON> i nazwisko',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'slug',
      title: 'URL (slug)',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      }
    },
    {
      name: 'image',
      title: 'Zd<PERSON><PERSON><PERSON>',
      type: 'image',
      options: {
        hotspot: true
      },
      fields: [
        {
          name: 'alt',
          title: 'Tekst alternatywny',
          type: 'string'
        }
      ]
    },
    {
      name: 'bio',
      title: 'Biografia',
      type: 'array',
      of: [
        {
          type: 'block'
        }
      ]
    },
    {
      name: 'credentials',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      type: 'array',
      of: [{type: 'string'}],
      description: 'Np. "RYT 500", "Magister fiz<PERSON>pii"'
    },
    {
      name: 'socialMedia',
      title: 'Media społecznościowe',
      type: 'object',
      fields: [
        {
          name: 'instagram',
          title: 'Instagram',
          type: 'url'
        },
        {
          name: 'facebook',
          title: 'Facebook',
          type: 'url'
        },
        {
          name: 'website',
          title: 'Strona internetowa',
          type: 'url'
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'name',
      media: 'image'
    }
  }
}
